"use client";

import { useState, useEffect } from "react";
import type { ChangeEvent, FormEvent} from "react";
import { ChevronDownIcon } from "lucide-react";
import {
    Card,
    CardHeader,
    CardTitle,
    CardDescription,
    CardContent,
    CardFooter,
} from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
    InputGroup,
    InputGroupAddon,
    InputGroupButton,
    InputGroupInput,
} from "@/components/ui/input-group";
import { AlertError } from "@/components/errors";
import { AlertSuccess } from "@/components/success";
import type { Item } from "@/lib/api/items/models";
import type { Receipt, Item as ReceiptItem } from "@/lib/api/receipts/models";
import type { Sale } from "@/lib/api/sales/models";

interface ExchangeSchema {
    original_sale_id: string;
    original_item_id: string;
    quantity_exchanged: number;
    quantity_receiving?: number;
    exchange_reason?: string;
    exchanged_with_item_id?: string;
    exchange_value?: number;
    receipt_number: string;
    comments?: string;
}

interface ExchangeFormProps {
    initialData?: ExchangeSchema;
    onSubmit(data: ExchangeSchema): Promise<void>;
    isEditing?: boolean;
    items: Item[];
    receipts: Receipt[];
    sales: Sale[];
}

export function ExchangeForm({
    initialData,
    onSubmit,
    isEditing = false,
    items,
    receipts,
    sales,
}: ExchangeFormProps) {
    const [loading, setLoading] = useState(false);
    const [formData, setFormData] = useState<ExchangeSchema>(
        initialData || {
            original_sale_id: "",
            original_item_id: "",
            quantity_exchanged: 1,
            quantity_receiving: 1,
            exchange_reason: "",
            exchanged_with_item_id: "",
            exchange_value: 0,
            receipt_number: "",
            comments: "",
        },
    );
    const [error, setError] = useState<string | null>(null);
    const [success, setSuccess] = useState<boolean>(false);

    // Update exchange value in real-time when relevant fields change
    useEffect(() => {
        if (formData.original_item_id && formData.exchanged_with_item_id && formData.receipt_number) {
            const originalItem = items.find((item) => item.id === formData.original_item_id);
            const newItem = items.find((item) => item.id === formData.exchanged_with_item_id);
            const receipt = receipts.find((receipt) => receipt.id === formData.receipt_number);
            const receiptItem = receipt?.items.find(item => item.name === originalItem?.name);

            if (originalItem && newItem && receiptItem) {
                const originalPrice = receiptItem.quantity > 0 
                                     ? Number(receiptItem.amount) / Number(receiptItem.quantity)
                                     : Number(originalItem.default_price) || 0;
                const newPrice = Number(newItem.default_price) || 0;
                const quantityReturning = Number(formData.quantity_exchanged) || 1;
                const quantityReceiving = Number(formData.quantity_receiving) || 1;
                
                const returningTotal = originalPrice * quantityReturning;
                const receivingTotal = newPrice * quantityReceiving;
                const difference = receivingTotal - returningTotal;
                
                setFormData(prev => ({
                    ...prev,
                    exchange_value: Number(difference.toFixed(2)),
                }));
            }
        }
    }, [formData.original_item_id, formData.exchanged_with_item_id, formData.quantity_exchanged, formData.quantity_receiving, formData.receipt_number, items, receipts]);

    const handleInputChange = (
        e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
    ) => {
        const { name, value } = e.target;
        const parsedValue = name === "quantity_exchanged" ? parseFloat(value) || 0 : value;

        setFormData((prev) => {
            const updatedFormData = {
                ...prev,
                [name]: parsedValue,
            };

            // Recalculate exchange value when quantity changes
            if (name === "quantity_exchanged") {
                return updateExchangeValueWithQuantity(
                    updatedFormData,
                    items,
                    formData.original_item_id,
                    formData.exchanged_with_item_id,
                    typeof parsedValue === "number" ? parsedValue : Number(parsedValue) || 0
                );
            }

            return updatedFormData;
        });
    };

    const handleSelectChange = (value: string, name: string) => {
        setFormData((prev) => {
            const updatedFormData = {
                ...prev,
                [name]: value,
            };

            if (name === "receipt_number") {
                return {
                    ...updatedFormData,
                    original_item_id: "",
                    original_sale_id: sales.find(sale => sale.receipt_id === value)?.id || "",
                    quantity_exchanged: 1, // Reset quantity on receipt change
                    exchange_value: 0, // Reset exchange value on receipt change
                };
            }

            // Calculate exchange value with updated data
            if (name === "exchanged_with_item_id" && prev.original_item_id) {
                const originalItem = items.find((item) => item.id === prev.original_item_id);
                const newItem = items.find((item) => item.id === value);
                const receipt = receipts.find((receipt) => receipt.id === prev.receipt_number);
                const receiptItem = receipt?.items.find(item => item.name === originalItem?.name);

                if (originalItem && newItem && receiptItem) {
                    // Calculate unit price from receipt: amount / quantity
                    const originalPrice = receiptItem.quantity > 0 
                                         ? Number(receiptItem.amount) / Number(receiptItem.quantity)
                                         : Number(originalItem.default_price) || 0;
                    const newPrice = Number(newItem.default_price) || 0;
                    const quantity = Number(prev.quantity_exchanged) || 1;
                    const difference = (newPrice - originalPrice) * quantity;
                    
                    return {
                        ...updatedFormData,
                        exchange_value: Number(difference.toFixed(2)),
                    };
                }
            } else if (name === "original_item_id" && prev.exchanged_with_item_id) {
                const originalItem = items.find((item) => item.id === value);
                const newItem = items.find((item) => item.id === prev.exchanged_with_item_id);
                const receipt = receipts.find((receipt) => receipt.id === prev.receipt_number);
                const receiptItem = receipt?.items.find(item => item.name === originalItem?.name);

                if (originalItem && newItem && receiptItem) {
                    // Calculate unit price from receipt: amount / quantity
                    const originalPrice = receiptItem.quantity > 0 
                                         ? Number(receiptItem.amount) / Number(receiptItem.quantity)
                                         : Number(originalItem.default_price) || 0;
                    const newPrice = Number(newItem.default_price) || 0;
                    const quantity = Number(prev.quantity_exchanged) || 1;
                    const difference = (newPrice - originalPrice) * quantity;
                    
                    return {
                        ...updatedFormData,
                        exchange_value: Number(difference.toFixed(2)),
                    };
                }
            }

            return updatedFormData;
        });
    };

    const updateExchangeValue = (originalId: string, newId: string) => {
        const originalItem = items.find((item) => item.id === originalId);
        const newItem = items.find((item) => item.id === newId);
        const receipt = receipts.find((receipt) => receipt.id === formData.receipt_number);
        const receiptItem = receipt?.items.find(item => item.name === originalItem?.name);

        if (originalItem && newItem && receiptItem) {
            // Use the actual price paid in the receipt, not the default price
            // Calculate unit price from receipt: amount / quantity
            const originalPrice = receiptItem.quantity > 0 
                                 ? Number(receiptItem.amount) / Number(receiptItem.quantity)
                                 : Number(originalItem.default_price) || 0;
            const newPrice = Number(newItem.default_price) || 0;
            const quantity = Number(formData.quantity_exchanged) || 1;
            const difference = (newPrice - originalPrice) * quantity;
            setFormData((prev) => ({
                ...prev,
                exchange_value: Number(difference.toFixed(2)),
            }));
        } else {
            setFormData((prev) => ({
                ...prev,
                exchange_value: 0,
            }));
        }
    };

    const updateExchangeValueWithQuantity = (
        formData: ExchangeSchema,
        items: Item[],
        originalId: string,
        newId: string | undefined,
        quantity: number
    ): ExchangeSchema => {
        const originalItem = items.find((item) => item.id === originalId);
        const newItem = newId ? items.find((item) => item.id === newId) : null;
        const receipt = receipts.find((receipt) => receipt.id === formData.receipt_number);
        const receiptItem = receipt?.items.find(item => item.name === originalItem?.name);

        let difference = 0;

        if (originalItem && receiptItem) {
            // Calculate unit price from receipt: amount / quantity
            const originalPrice = receiptItem.quantity > 0 
                                 ? Number(receiptItem.amount) / Number(receiptItem.quantity)
                                 : Number(originalItem.default_price) || 0; // Use actual receipt price
            const validQuantity = Number(quantity) || 1;
            if (newItem) {
                const newPrice = Number(newItem.default_price) || 0;
                difference = (newPrice - originalPrice) * validQuantity;
            } else {
                // If no new item selected, it's a refund scenario
                difference = -originalPrice * validQuantity;
            }

            return {
                ...formData,
                exchange_value: Number(difference.toFixed(2)),
            };
        }

        return {
            ...formData,
            exchange_value: 0,
        };
    };


    const handleFormSubmit = async (e: FormEvent) => {
        e.preventDefault();
        setLoading(true);
        setError(null);
        setSuccess(false);

        try {
            await onSubmit(formData);
            setSuccess(true);

            if (!isEditing) {
                setFormData({
                    original_sale_id: "",
                    original_item_id: "",
                    quantity_exchanged: 1,
                    exchange_reason: "",
                    exchanged_with_item_id: "",
                    exchange_value: 0,
                    receipt_number: "",
                    comments: "",
                });
            }
        } catch (error) {
            setError((error as Error).message);
        } finally {
            setLoading(false);
        }
    };

    return (
        <Card className="w-full max-w-3xl mx-auto">
            <CardHeader>
                {error && <AlertError message={error} />}
                {success && <AlertSuccess message={`Exchange ${isEditing ? 'updated' : 'processed'} successfully!`} />}
                <CardTitle>{isEditing ? 'Edit Exchange' : 'Product Exchange'}</CardTitle>
                <CardDescription>
                    {isEditing ? 'Edit the product exchange details' : 'Process a product exchange for returned items'}
                </CardDescription>
            </CardHeader>
            <CardContent>
                <form onSubmit={handleFormSubmit} className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                            <Label htmlFor="receipt_number">Receipt Number *</Label>
                            <InputGroup>
                                <InputGroupInput 
                                    placeholder="Select original receipt" 
                                    value={receipts.find(r => r.id === formData.receipt_number)?.receipt_number || ''}
                                    readOnly
                                />
                                <InputGroupAddon align="inline-end">
                                    <DropdownMenu>
                                        <DropdownMenuTrigger asChild>
                                            <InputGroupButton variant="ghost" className="!pr-1.5 text-xs">
                                                Select <ChevronDownIcon className="size-3" />
                                            </InputGroupButton>
                                        </DropdownMenuTrigger>
                                        <DropdownMenuContent align="end">
                                            {receipts.map((receipt) => (
                                                <DropdownMenuItem 
                                                    key={receipt.id} 
                                                    onClick={() => handleSelectChange(receipt.id, "receipt_number")}
                                                >
                                                    {receipt.receipt_number} - {receipt.salesperson.split(' ')[0]} {new Date(receipt.created_at).toLocaleString('en-US')}
                                                </DropdownMenuItem>
                                            ))}
                                        </DropdownMenuContent>
                                    </DropdownMenu>
                                </InputGroupAddon>
                            </InputGroup>
                        </div>

                        <div className="space-y-2">
                            <Label htmlFor="original_sale_id">Original Sale ID *</Label>
                            <Input
                                required
                                id="original_sale_id"
                                name="original_sale_id"
                                value={sales.find((sale) => sale.receipt_id === formData.receipt_number)?.id}
                                disabled
                                onChange={handleInputChange}
                                placeholder="Enter original sale ID"
                            />
                        </div>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                            <Label htmlFor="original_item_id">Original Item *</Label>
                            <InputGroup>
                                <InputGroupInput 
                                    placeholder="Select original item" 
                                    value={(() => {
                                        const selectedItem = items.find(item => item.id === formData.original_item_id);
                                        const receiptItem = receipts
                                            .find(receipt => receipt.id === formData.receipt_number)
                                            ?.items.find(item => item.name === selectedItem?.name);
                                        if (!receiptItem) return '';
                                        const unitPrice = receiptItem.quantity > 0 ? receiptItem.amount / receiptItem.quantity : 0;
                                        return `${receiptItem.name} - $${unitPrice.toFixed(2)} (Qty: ${receiptItem.quantity})`;
                                    })()} 
                                    readOnly
                                />
                                <InputGroupAddon align="inline-end">
                                    <DropdownMenu>
                                        <DropdownMenuTrigger asChild>
                                            <InputGroupButton variant="ghost" className="!pr-1.5 text-xs">
                                                Select <ChevronDownIcon className="size-3" />
                                            </InputGroupButton>
                                        </DropdownMenuTrigger>
                                        <DropdownMenuContent align="end">
                                            {receipts
                                                .find((receipt) => receipt.id === formData.receipt_number)
                                                ?.items.sort((a, b) => a.name.localeCompare(b.name))
                                                .map((receiptItem: ReceiptItem) => {
                                                    const product = items.find((product) => product.name === receiptItem.name);
                                                    if (!product) return null;
                                                    return (
                                                        <DropdownMenuItem 
                                                            key={product.id} 
                                                            onClick={() => handleSelectChange(product.id, "original_item_id")}
                                                        >
                                                            {(() => {
                                                                const unitPrice = receiptItem.quantity > 0 ? receiptItem.amount / receiptItem.quantity : 0;
                                                                return `${receiptItem.name} - $${unitPrice.toFixed(2)} (Qty: ${receiptItem.quantity})`;
                                                            })()}
                                                        </DropdownMenuItem>
                                                    );
                                                })}
                                        </DropdownMenuContent>
                                    </DropdownMenu>
                                </InputGroupAddon>
                            </InputGroup>
                        </div>

                        <div className="space-y-2">
                            <Label htmlFor="exchanged_with_item_id">Exchange With Item</Label>
                            <InputGroup>
                                <InputGroupInput 
                                    placeholder="Select new item" 
                                    value={(() => {
                                        const selectedItem = items.find(item => item.id === formData.exchanged_with_item_id);
                                        return selectedItem ? `${selectedItem.name} - $${selectedItem.default_price}` : '';
                                    })()} 
                                    readOnly
                                />
                                <InputGroupAddon align="inline-end">
                                    <DropdownMenu>
                                        <DropdownMenuTrigger asChild>
                                            <InputGroupButton variant="ghost" className="!pr-1.5 text-xs">
                                                Select <ChevronDownIcon className="size-3" />
                                            </InputGroupButton>
                                        </DropdownMenuTrigger>
                                        <DropdownMenuContent align="end">
                                            {items.map((item) => (
                                                <DropdownMenuItem 
                                                    key={item.id} 
                                                    onClick={() => handleSelectChange(item.id, "exchanged_with_item_id")}
                                                >
                                                    {item.name} - ${item.default_price}
                                                </DropdownMenuItem>
                                            ))}
                                        </DropdownMenuContent>
                                    </DropdownMenu>
                                </InputGroupAddon>
                            </InputGroup>
                        </div>
                    </div>

                    <div className="grid grid-cols-3 gap-4">
                        <div className="space-y-2">
                            <Label htmlFor="quantity_returned">Quantity Returning *</Label>
                            <Input
                                required
                                type="number"
                                min="1"
                                max={(() => {
                                    const selectedItem = items.find(item => item.id === formData.original_item_id);
                                    const receiptItem = receipts
                                        .find(receipt => receipt.id === formData.receipt_number)
                                        ?.items.find(item => item.name === selectedItem?.name);
                                    return receiptItem?.quantity || 1;
                                })()}
                                id="quantity_returned"
                                name="quantity_returned"
                                value={formData.quantity_exchanged}
                                onChange={(e) => {
                                    const maxAllowed = (() => {
                                        const selectedItem = items.find(item => item.id === formData.original_item_id);
                                        const receiptItem = receipts
                                            .find(receipt => receipt.id === formData.receipt_number)
                                            ?.items.find(item => item.name === selectedItem?.name);
                                        return receiptItem?.quantity || 1;
                                    })();
                                    const value = Math.min(parseInt(e.target.value) || 1, maxAllowed);
                                    setFormData(prev => ({ ...prev, quantity_exchanged: value }));
                                }}
                            />
                            <p className="text-xs text-gray-500">
                                {(() => {
                                    const selectedItem = items.find(item => item.id === formData.original_item_id);
                                    const receiptItem = receipts
                                        .find(receipt => receipt.id === formData.receipt_number)
                                        ?.items.find(item => item.name === selectedItem?.name);
                                    if (!receiptItem) return '';
                                    
                                    const totalPurchased = receiptItem.quantity;
                                    return `Max returnable: ${totalPurchased}`;
                                })()} 
                            </p>
                        </div>
                        
                        <div className="space-y-2">
                            <Label htmlFor="quantity_receiving">Quantity Receiving</Label>
                            <Input
                                type="number"
                                min="0"
                                id="quantity_receiving"
                                name="quantity_receiving"
                                value={formData.quantity_receiving || 0}
                                onChange={(e) => {
                                    const value = parseInt(e.target.value) || 0;
                                    setFormData(prev => ({ ...prev, quantity_receiving: value }));
                                }}
                            />
                            <p className="text-xs text-gray-500">
                                Enter 0 for refund only
                            </p>
                        </div>

                        <div className="space-y-2">
                            <Label htmlFor="exchange_value">Price Difference</Label>
                            <div className="relative">
                                <Input
                                    type="text"
                                    id="exchange_value"
                                    name="exchange_value"
                                    value={(() => {
                                        const value = formData.exchange_value ?? 0;
                                        if (isNaN(value) || value === 0) return "$0.00";
                                        const sign = value > 0 ? "+" : "";
                                        return `${sign}$${value.toFixed(2)}`;
                                    })()}
                                    placeholder="$0.00"
                                    disabled
                                    className={`${
                                        (formData.exchange_value ?? 0) > 0 
                                            ? "text-red-600" 
                                            : (formData.exchange_value ?? 0) < 0 
                                            ? "text-green-600" 
                                            : "text-gray-600"
                                    }`}
                                />
                            </div>
                            <p className="text-xs text-gray-500">
                                {(formData.exchange_value ?? 0) > 0 
                                    ? "Customer owes additional amount" 
                                    : (formData.exchange_value ?? 0) < 0 
                                    ? "Refund amount to customer" 
                                    : "No price difference"}
                            </p>
                            
                            {formData.original_item_id && formData.exchanged_with_item_id && (
                                <div className="mt-2 p-2 bg-gray-50 rounded text-xs">
                                    <div className="font-medium mb-1">Exchange Summary:</div>
                                    {(() => {
                                        const originalItem = items.find(item => item.id === formData.original_item_id);
                                        const newItem = items.find(item => item.id === formData.exchanged_with_item_id);
                                        const receipt = receipts.find(receipt => receipt.id === formData.receipt_number);
                                        const receiptItem = receipt?.items.find(item => item.name === originalItem?.name);
                                        
                                        if (!originalItem || !newItem || !receiptItem) return null;
                                        
                                        // Calculate unit price from receipt: amount / quantity
                                        const originalPrice = receiptItem.quantity > 0 
                                                             ? Number(receiptItem.amount) / Number(receiptItem.quantity)
                                                             : Number(originalItem.default_price) || 0;
                                        const newPrice = Number(newItem.default_price) || 0;
                                        const quantityReturning = Number(formData.quantity_exchanged) || 1;
                                        const quantityReceiving = Number(formData.quantity_receiving) || 1;
                                        
                                        // Calculate totals
                                        const returningTotal = originalPrice * quantityReturning;
                                        const receivingTotal = newPrice * quantityReceiving;
                                        const calculatedDifference = receivingTotal - returningTotal;
                                        
                                        // Debug log to see the calculation
                                        console.log('Exchange Calculation Debug:', {
                                            receiptItem,
                                            originalPrice,
                                            newPrice,
                                            quantityReturning,
                                            quantityReceiving,
                                            returningTotal,
                                            receivingTotal,
                                            calculatedDifference,
                                            formDataExchangeValue: formData.exchange_value,
                                            originalItemName: originalItem.name
                                        });
                                        
                                        return (
                                            <div className="space-y-1">
                                                <div>Returning: {quantityReturning}x {originalItem.name} @ ${originalPrice.toFixed(2)} = ${returningTotal.toFixed(2)}</div>
                                                <div>Receiving: {quantityReceiving}x {newItem.name} @ ${newPrice.toFixed(2)} = ${receivingTotal.toFixed(2)}</div>
                                                <div className="border-t pt-1 font-medium">
                                                    Net: {calculatedDifference >= 0 ? '+' : ''}${calculatedDifference.toFixed(2)}
                                                </div>
                                            </div>
                                        );
                                    })()}
                                </div>
                            )}
                        </div>
                    </div>

                    <div className="space-y-2">
                        <Label htmlFor="exchange_reason">Exchange Reason</Label>
                        <Textarea
                            id="exchange_reason"
                            name="exchange_reason"
                            value={formData.exchange_reason}
                            onChange={handleInputChange}
                            placeholder="Enter reason for exchange"
                            className="min-h-[80px]"
                        />
                    </div>

                    <div className="space-y-2">
                        <Label htmlFor="comments">Additional Comments</Label>
                        <Textarea
                            id="comments"
                            name="comments"
                            value={formData.comments}
                            onChange={handleInputChange}
                            placeholder="Add any additional comments"
                            className="min-h-[80px]"
                        />
                    </div>
                </form>
            </CardContent>
            <CardFooter className="flex justify-end space-x-4">
                <Button
                    variant="outline"
                    onClick={() =>
                        setFormData({
                            original_sale_id: "",
                            original_item_id: "",
                            quantity_exchanged: 1,
                            quantity_receiving: 1,
                            exchange_reason: "",
                            exchanged_with_item_id: "",
                            exchange_value: 0,
                            receipt_number: "",
                            comments: "",
                        })
                    }
                >
                    Clear
                </Button>
                <Button onClick={handleFormSubmit} disabled={loading}>
                    {loading ? "Processing..." : isEditing ? "Update Exchange" : "Process Exchange"}
                </Button>
            </CardFooter>
        </Card>
    );
}
