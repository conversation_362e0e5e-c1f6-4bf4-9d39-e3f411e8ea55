import { BASE_URL } from "@/routes/configs/constants";


export async function uploadFile(
    authToken: string,
    folder: string,
    file: File,
): Promise<string> {
    const url = `${BASE_URL}/upload/${folder}`;
    const headers = {
        Authorization: `Bearer ${authToken}`,
    };

    const formData = new FormData();
    formData.append("file", file);

    try {
        const response = await fetch(url, {
            method: "POST",
            headers,
            body: formData,
        });

        if (!response.ok) {
            throw new Error(`Failed to upload file: ${response.statusText}`);
        }

        // Parse the response to get the file URL
        const data = await response.json();
        return data.location; // Assuming the backend returns { location: "http://..." }
    } catch (error) {
        console.error("Error uploading file:", error);
        throw error;
    }
}


export async function uploadFiles(
    authToken: string,
    folder: string,
    files: File[],
): Promise<string[]> {
    const uploadPromises = files.map((file) => uploadFile(authToken, folder, file));
    return Promise.all(uploadPromises);
}


// lib/firebase-upload.ts
export interface SignedUrlResponse {
    upload_url: string;
  }
  
  /**
   * Get signed upload URL from your Rust backend
   */
  async function getSignedUploadUrl(
    folder: string,
    filename: string,
    expiresInSeconds?: number
  ): Promise<string> {
    const params = new URLSearchParams({
      folder,
      filename,
      ...(expiresInSeconds && { expires_in_seconds: expiresInSeconds.toString() }),
    });
  
    const response = await fetch(`/api/upload-url?${params}`);
    
    if (!response.ok) {
      throw new Error(`Failed to get signed URL: ${response.statusText}`);
    }
  
    const data: SignedUrlResponse = await response.json();
    return data.upload_url;
  }
  
  /**
   * Upload file directly to Firebase Storage using signed URL
   */
  async function uploadFileWithSignedUrl(file: File, uploadUrl: string): Promise<void> {
    const response = await fetch(uploadUrl, {
      method: 'PUT',
      body: file,
      headers: {
        'Content-Type': file.type,
      },
    });
  
    if (!response.ok) {
      throw new Error(`Upload failed: ${response.status} ${response.statusText}`);
    }
  }
  
  /**
   * Updated upload function using signed URLs
   * @param file - The file to upload
   * @param folder - The folder path (e.g., 'products', 'store_logos')
   * @param storeId - The store ID for organizing files
   * @param fileName - Optional custom filename, if not provided, will generate one
   * @returns Promise<string> - The file path in Firebase Storage
   */
  export async function uploadFileToFirebase(
    file: File,
    folder: string,
    storeId: string,
    fileName?: string
  ): Promise<string> {
    try {
      // Generate filename if not provided (keeping your existing logic)
      const timestamp = Date.now();
      const fileExtension = file.name.split('.').pop();
      const finalFileName = fileName || `${timestamp}_${Math.random().toString(36).substring(2)}.${fileExtension}`;
  
      // Create file path based on folder type (keeping your existing logic)
      let finalFilePath: string;
      if (folder === 'products') {
        finalFilePath = `${storeId}/${finalFileName}`;
      } else if (folder === 'store_logos') {
        const logoFileName = `${storeId}.${fileExtension}`;
        finalFilePath = logoFileName;
      } else {
        finalFilePath = `${storeId}/${finalFileName}`;
      }
  
      // Get signed upload URL from your Rust backend
      const uploadUrl = await getSignedUploadUrl(folder, finalFilePath, 3600);
  
      // Upload file directly to Firebase Storage
      await uploadFileWithSignedUrl(file, uploadUrl);
  
      // Return the full path for reference
      const fullPath = `${folder}/${finalFilePath}`;
      console.log('File uploaded successfully to Firebase:', fullPath);
      return fullPath;
  
    } catch (error) {
      console.error('Error uploading file to Firebase:', error);
      throw new Error(`Failed to upload file: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }
  
  /**
   * Upload multiple files to Firebase Storage
   * @param files - Array of files to upload
   * @param folder - The folder path
   * @param storeId - The store ID for organizing files
   * @returns Promise<string[]> - Array of file paths
   */
  export async function uploadFilesToFirebase(
    files: File[],
    folder: string,
    storeId: string
  ): Promise<string[]> {
    try {
      const uploadPromises = files.map(file => uploadFileToFirebase(file, folder, storeId));
      return await Promise.all(uploadPromises);
    } catch (error) {
      console.error('Error uploading multiple files to Firebase:', error);
      throw error;
    }
  }