// app/app/integrations/mpesa/MpesaIntegrationForm.tsx
"use client";

import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardHeader,
  CardTitle,
  CardDescription,
  CardContent,
  CardFooter,
} from "@/components/ui/card";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { AlertCircle, CheckCircle2, Loader2, ArrowLeft, Eye, EyeOff } from "lucide-react";
// import { Link } from "@remix-run/react";
import type { MpesaIntegrationFormValues } from "@/lib/api/integrations/mpesa/models";
import React from "react";
import Cookies from "js-cookie";
import { Link } from "react-router";


const formSchema = z.object({
  business_short_code: z.string().min(1, "Business short code is required"),
  consumer_key: z.string().min(1, "Consumer key is required"),
  consumer_secret: z.string().min(1, "Consumer secret is required"),
  pass_key: z.string().min(1, "Pass key is required"),
  store_id: z.string(),
});

interface MpesaIntegrationFormProps {
  initialData: MpesaIntegrationFormValues;
  onSubmit: (values: MpesaIntegrationFormValues) => Promise<void>;
  isEditing?: boolean;
}

export function MpesaIntegrationForm({
  initialData,
  onSubmit,
  isEditing = false,
}: MpesaIntegrationFormProps) {
  const [error, setError] = React.useState<string | null>(null);
  const [success, setSuccess] = React.useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = React.useState(false);
  const [showConsumerKey, setShowConsumerKey] = React.useState(false);
  const [showConsumerSecret, setShowConsumerSecret] = React.useState(false);
  const [showPassKey, setShowPassKey] = React.useState(false);

  const store_id = Cookies.get("active_store");

  const form = useForm<MpesaIntegrationFormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      ...initialData,
      store_id: store_id || "",
    },
  });

  const handleSubmit = async (values: MpesaIntegrationFormValues) => {
    try {
      setIsSubmitting(true);
      setError(null);
      setSuccess(null);
      await onSubmit({
        ...values,
        store_id: store_id || "",
      });
      setSuccess(
        isEditing
          ? "M-Pesa integration updated successfully"
          : "M-Pesa integration created successfully"
      );
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to save M-Pesa integration");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="container mx-auto py-4 md:py-8">
      <div className="mb-4">
        <Link to="/app/integrations/mpesa" className="flex items-center text-sm text-muted-foreground">
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to M-Pesa Integration
        </Link>
      </div>
      
      <Card>
        <CardHeader>
          <CardTitle>
            {isEditing ? "Update M-Pesa Integration" : "Set Up M-Pesa Integration"}
          </CardTitle>
          <CardDescription>
            {isEditing
              ? "Update your M-Pesa payment gateway credentials"
              : "Configure your M-Pesa payment gateway to start accepting payments"}
          </CardDescription>
        </CardHeader>
        <CardContent>
          {error && (
            <Alert variant="destructive" className="mb-6">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Error</AlertTitle>
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}
          {success && (
            <Alert variant="default" className="mb-6">
              <CheckCircle2 className="h-4 w-4" />
              <AlertTitle>Success</AlertTitle>
              <AlertDescription>{success}</AlertDescription>
            </Alert>
          )}
          <Form {...form}>
            <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <FormField
                control={form.control}
                name="business_short_code"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Business Short Code</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Enter your business short code"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="consumer_key"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Consumer Key</FormLabel>
                    <FormControl>
                      <div className="relative">
                        <Input
                          type={showConsumerKey ? "text" : "password"}
                          placeholder="Enter your consumer key"
                          {...field}
                        />
                        <button
                          type="button"
                          onClick={() => setShowConsumerKey(!showConsumerKey)}
                          className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-500 hover:text-gray-700"
                        >
                          {showConsumerKey ? (
                            <EyeOff className="h-4 w-4" />
                          ) : (
                            <Eye className="h-4 w-4" />
                          )}
                        </button>
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="consumer_secret"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Consumer Secret</FormLabel>
                    <FormControl>
                      <div className="relative">
                        <Input
                          type={showConsumerSecret ? "text" : "password"}
                          placeholder="Enter your consumer secret"
                          {...field}
                        />
                        <button
                          type="button"
                          onClick={() => setShowConsumerSecret(!showConsumerSecret)}
                          className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-500 hover:text-gray-700"
                        >
                          {showConsumerSecret ? (
                            <EyeOff className="h-4 w-4" />
                          ) : (
                            <Eye className="h-4 w-4" />
                          )}
                        </button>
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="pass_key"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Pass Key</FormLabel>
                    <FormControl>
                      <div className="relative">
                        <Input
                          type={showPassKey ? "text" : "password"}
                          placeholder="Enter your pass key"
                          {...field}
                        />
                        <button
                          type="button"
                          onClick={() => setShowPassKey(!showPassKey)}
                          className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-500 hover:text-gray-700"
                        >
                          {showPassKey ? (
                            <EyeOff className="h-4 w-4" />
                          ) : (
                            <Eye className="h-4 w-4" />
                          )}
                        </button>
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              </div>
              <div className="flex justify-end">
                <Button type="submit" disabled={isSubmitting} className="w-full md:w-auto">
                  {isSubmitting ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : isEditing ? (
                    "Update Integration"
                  ) : (
                    "Save Integration"
                  )}
                </Button>
              </div>
            </form>
          </Form>
        </CardContent>
        <CardFooter>
          <p className="text-sm text-muted-foreground">
            Note: All credentials are encrypted and stored securely.
          </p>
        </CardFooter>
      </Card>
    </div>
  );
}