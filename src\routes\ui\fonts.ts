
// export const inter = Inter({ subsets: ["latin"] });

// export const lusitana = Lusitana({
//   weight: ["400", "700"],
//   subsets: ["latin"],
// });

// export const roboto = Roboto({
//   weight: ["400", "700"],
//   subsets: ["latin"],
// });

import "@fontsource/inter/400.css";
import "@fontsource/inter/700.css";
import "@fontsource/roboto/400.css";
import "@fontsource/roboto/700.css";
import "@fontsource/lusitana/400.css";
import "@fontsource/lusitana/700.css";

export const inter = {
  className: "font-inter",
  style: { fontFamily: "Inter, sans-serif" },
};

export const roboto = {
  className: "font-roboto",
  style: { fontFamily: "Roboto, sans-serif" },
};

export const lusitana = {
  className: "font-lusitana",
  style: { fontFamily: "Lusitana, serif" },
};
