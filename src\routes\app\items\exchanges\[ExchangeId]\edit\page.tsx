"use client";

import { useState, useEffect } from "react";
import Cookies from "js-cookie";
import { ExchangeForm } from "@/routes/app/items/exchanges/ExchangeForm";
import type { Item } from "@/lib/api/items/models";
import { fetchItems } from "@/lib/api/items/service";
import { getExchange, updateExchange } from "@/lib/api/items/exchanges/service";
import type { ExchangeSchema } from "@/lib/api/items/exchanges/models";
import type { Receipt } from "@/lib/api/receipts/models";
import { fetchReceipts } from "@/lib/api/receipts/service";
import type { Sale } from "@/lib/api/sales/models";
import { fetchSales } from "@/lib/api/sales/service";
import { Link, useNavigate, useParams } from "react-router";;
import { Button } from "@/components/ui/button";

// import { inter } from "@/app/ui/fonts";

export default function EditExchangePage() {
  const [items, setItems] = useState<Item[]>([]);
  const [receipts, setReceipts] = useState<Receipt[]>([]);
  const [sales, setSales] = useState<Sale[]>([]);
  const [initialData, setInitialData] = useState<ExchangeSchema | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [loadingMessage, setLoadingMessage] = useState<string>('Fetching data...');
  const [error, setError] = useState<Error | null>(null);
  const [isNotFound, setIsNotFound] = useState<boolean>(false);

  const router = useNavigate();
  const token = Cookies.get("auth_token");
  const store_id = Cookies.get("active_store");
  const exchangeId = useParams().ExchangeId ?? "0000-0000-0000-0000-000000000000";

  useEffect(() => {
    const fetchData = async () => {
      if (!token || !store_id) {
        setError(new Error("Authentication token or store ID is missing. Please log in again."));
        setIsLoading(false);
        return;
      }

      setIsLoading(true);
      setError(null);
      setIsNotFound(false);
      setLoadingMessage('Fetching items, receipts, sales, and exchange data...');

      try {
        const [fetchedItems, fetchedReceipts, fetchedSales, exchange] = await Promise.all([
          fetchItems(token, store_id),
          fetchReceipts(token, store_id),
          fetchSales(token, store_id),
          getExchange(token, store_id, exchangeId),
          new Promise(resolve => setTimeout(resolve, 1000)) // Minimum 1s loading for testing
        ]);
        setItems(fetchedItems);
        setReceipts(fetchedReceipts);
        setSales(fetchedSales);
        setInitialData(exchange);
      } catch (err: any) {
        if (err.message === "Exchange not found") {
          setIsNotFound(true);
        } else {
          setError(err instanceof Error ? err : new Error("Failed to fetch data."));
        }
      } finally {
        setIsLoading(false);
      }
    };
    fetchData();
  }, [token, store_id, exchangeId]);

  const handleSubmit = async (data: ExchangeSchema) => {
    if (!token || !store_id) {
      throw new Error("Authentication token or store ID is missing.");
    }

    try {
      await updateExchange(token, store_id, exchangeId, data);
      router("/app/items/exchanges");
    } catch (error) {
      setError(error instanceof Error ? error : new Error("Error updating exchange."));
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex justify-center items-center bg-gray-50">
        <div className="animate-pulse flex flex-col items-center">
          <div className="h-12 w-12 rounded-full border-4 border-t-blue-500 border-gray-200 animate-spin"></div>
          <p className="mt-4 text-gray-600 font-medium">{loadingMessage}</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex justify-center items-center bg-gray-50">
        <div className="bg-white p-8 rounded-lg shadow-md max-w-md w-full">
          <div className="text-red-500 flex items-center justify-center mb-4">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <h2 className="text-xl font-bold text-center mb-2">Error Loading Exchange Data</h2>
          <p className="text-gray-600 text-center">{error.message}</p>
          <div className="mt-6 flex justify-center">
            <Link to="/app/items/exchanges">
              <Button variant="link">Return to Exchanges List</Button>
            </Link>
          </div>
        </div>
      </div>
    );
  }

  if (isNotFound) {
    return (
      <div className="min-h-screen flex justify-center items-center bg-gray-50">
        <div className="bg-white p-8 rounded-lg shadow-md max-w-md w-full">
          <div className="text-amber-500 flex items-center justify-center mb-4">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <h2 className="text-xl font-bold text-center mb-2">Exchange Not Found</h2>
          <p className="text-gray-600 text-center">The exchange with ID ({exchangeId}) could not be found.</p>
          <div className="mt-6 flex justify-center">
            <Link to="/app/items/exchanges">
              <Button variant="link">Return to Exchanges List</Button>
            </Link>
          </div>
        </div>
      </div>
    );
  }

  if (!initialData) {
    return (
      <div className="min-h-screen flex justify-center items-center bg-gray-50">
        <p className="text-gray-600">Exchange data could not be prepared for editing.</p>
        <Link to="/app/items/exchanges" className="ml-4">
          <Button variant="link">Return to Exchanges List</Button>
        </Link>
      </div>
    );
  }

  return (
    <div className="bg-gray-50 min-h-screen">
      <div className="bg-white shadow-sm">
        <div className="container mx-auto px-4 py-3">
          <div className="flex text-sm text-gray-500 items-center">
            <Link to="/app/dashboard" className="hover:text-blue-600">Dashboard</Link>
            <span className="mx-2">/</span>
            <Link to="/app/items/exchanges" className="hover:text-blue-600">Exchanges</Link>
            <span className="mx-2">/</span>
            <span className="text-gray-700 font-medium">Edit Exchange: {exchangeId}</span>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 pt-6 pb-6 flex justify-center">
        <div className="w-full max-w-2xl">
          <ExchangeForm
            initialData={initialData}
            onSubmit={handleSubmit}
            items={items}
            isEditing={true}
            receipts={receipts}
            sales={sales}          />
        </div>
      </div>
    </div>
  );
}