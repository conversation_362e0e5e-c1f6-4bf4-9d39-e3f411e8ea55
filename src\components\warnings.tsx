import { useState } from "react";
import { AlertCircle, X } from "lucide-react";

import {
  Alert,
  AlertDescription,
  AlertTitle,
} from "@/components/ui/alert";
import { Button } from "@/components/ui/button";

export function AlertWarning({ message }: { message: string }) {
  const [isVisible, setIsVisible] = useState(true);

  if (!isVisible) {
    return null;
  }

  return (
    <Alert variant="default">
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          <AlertCircle className="h-4 w-4 mr-2" />
          <div>
            <AlertTitle>Warning!</AlertTitle>
            <AlertDescription>{message}</AlertDescription>
          </div>
        </div>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setIsVisible(false)}
          aria-label="Close error alert"
        >
          <X className="h-4 w-4" />
        </Button>
      </div>
    </Alert>
  );
}
