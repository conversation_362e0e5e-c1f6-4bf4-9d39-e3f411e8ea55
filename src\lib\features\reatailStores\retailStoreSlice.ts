
import type { Store as RetailStore } from "@/lib/api/retailStores/models";
import { type PayloadAction } from "@reduxjs/toolkit";
import { createSlice, type Slice } from "@reduxjs/toolkit";

interface RetailStoreState {
    stores: RetailStore[];
    selectedStore: RetailStore | null;
    loading: boolean;
    error: string | null;
}

const initialState: RetailStoreState = {
    stores: [],
    selectedStore: null,
    loading: false,
    error: null,
};


interface SetStoresAction {
    payload: RetailStore[];
}

interface SetSelectedStoreAction {
    payload: RetailStore;
}

interface AddStoreAction {
    payload: RetailStore;
}

interface DeleteStoreAction {
    payload: string;
}

interface SetLoadingAction {
    payload: boolean;
}

interface SetErrorAction {
    payload: string;
}

interface UpdateStoreAction {
    payload: RetailStore;
}

const retailStoreSlice: Slice<RetailStoreState> = createSlice({
    name: 'retailStores',
    initialState,
    reducers: {
        setStores: (state, action: PayloadAction<SetStoresAction['payload']>) => {
            state.stores = action.payload;
        },
        setSelectedStore: (state, action: PayloadAction<SetSelectedStoreAction['payload']>) => {
            state.selectedStore = action.payload;
        },
        addStore: (state, action: PayloadAction<AddStoreAction['payload']>) => {
            state.stores.push(action.payload);
        },
        updateStore: (state, action: PayloadAction<UpdateStoreAction['payload']>) => {
        
            const index = state.stores.findIndex(store => store.id === action.payload.id);
            if (index !== -1) {
                state.stores[index] = action.payload;
            }
        },

        deleteStore: (state, action: PayloadAction<DeleteStoreAction['payload']>) => {
            state.stores = state.stores.filter(store => store.id !== action.payload);
        },
        setLoading: (state, action: PayloadAction<SetLoadingAction['payload']>) => {
            state.loading = action.payload;
        },
        setError: (state, action: PayloadAction<SetErrorAction['payload']>) => {
            state.error = action.payload;
        },
    },
});


export const {setStores, setSelectedStore, addStore, updateStore, deleteStore, setLoading, setError} = retailStoreSlice.actions;

export default retailStoreSlice.reducer;