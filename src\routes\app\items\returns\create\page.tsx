"use client";

import { useState, useEffect } from "react";
import Cookies from "js-cookie";
import { ReturnForm } from "@/routes/app/items/returns/ReturnForm";
import type { Item } from "@/lib/api/items/models";
import { fetchItems } from "@/lib/api/items/service";
import { createReturn } from "@/lib/api/items/returns/service";
import type { ReturnSchema } from "@/lib/api/items/returns/models";
import type { Sale } from "@/lib/api/sales/models";
import { fetchSales } from "@/lib/api/sales/service";
import { fetchReceipts } from "@/lib/api/receipts/service";
import type { Receipt } from "@/lib/api/receipts/models";
import { useNavigate } from "react-router";
import { getMe } from "@/lib/api/users/service";

export default function CreateReturnPage() {
  const [items, setItems] = useState<Item[]>([]);
  const [sales, setSales] = useState<Sale[]>([]);
  const [receipts, setReceipts] = useState<Receipt[]>([]);
  const [userId, setUserId] = useState<string | null>(null);
  const router = useNavigate();

  const token = Cookies.get("auth_token");
  const store_id = Cookies.get("active_store");

  useEffect(() => {
    const fetchData = async () => {
      if (token && store_id) {
        try {
          const fetchedItems = await fetchItems(token, store_id);
          setItems(fetchedItems);
          const fetchedSales = await fetchSales(token, store_id);
          setSales(fetchedSales);
          const fetchedReceipts = await fetchReceipts(token, store_id);
          setReceipts(fetchedReceipts);
          const user_id = (await getMe(token)).id;
          setUserId(user_id);
        } catch (error) {
          console.error("Failed to fetch data:", error);
        }
      }
    };
    fetchData();
  }, [token, store_id]);

  const handleSubmit = async (data: ReturnSchema) => {
    if (!token || !store_id) {
      throw new Error("Authentication token or store ID is missing.");
    }

    try {
      await createReturn(token, store_id, data);
      router("/app/items/returns");
    } catch (err) {
      const error = err as Error;
      throw new Error(error.message || "Failed to create return.");
    }
  };

  if (!userId) {
    return <div>Loading user information...</div>;
  }

  return (
    <ReturnForm
      onSubmit={handleSubmit}
      items={items}
      sales={sales}
      receipts={receipts}
      userId={userId}
    />
  );
}