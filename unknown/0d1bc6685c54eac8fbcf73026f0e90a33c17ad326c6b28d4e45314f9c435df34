import React from 'react';
import { faker } from '@faker-js/faker';

interface RecentSale {
    id: string;
    customerName: string;
    email: string;
    amount: number;
}

const RecentSales = () => {
    const recentSales: RecentSale[] = React.useMemo(() =>
        Array.from({ length: 5 }, () => ({
            id: faker.string.uuid(),
            customerName: faker.person.fullName(),
            email: faker.internet.email(),
            amount: parseFloat(faker.commerce.price({ min: 1, max: 2000, dec: 2 })),
        })),
        []
    );

    return (
        <div>
            <h2 className="text-lg font-semibold mb-4">Recent Sales</h2>
            <p className="text-sm text-gray-500 mb-2">You made {recentSales.length} sales this month.</p>
            <div className="divide-y divide-gray-200 dark:divide-gray-700">
                {recentSales.map((sale) => (
                    <div key={sale.id} className="py-3 flex items-center justify-between">
                        <div>
                            <div className="text-sm font-medium text-gray-900 dark:text-white">{sale.customerName}</div>
                            <div className="text-sm text-gray-500 dark:text-gray-400">{sale.email}</div>
                        </div>
                        <div className="text-lg font-bold text-green-500">+${sale.amount.toFixed(2)}</div>
                    </div>
                ))}
            </div>
        </div>
    );
};

export default RecentSales;