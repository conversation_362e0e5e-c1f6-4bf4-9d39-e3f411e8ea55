
import { Line, LineChart, <PERSON>Axis, YAxis, ReferenceLine } from 'recharts';
import { type ChartConfig, ChartContainer, ChartTooltip } from '@/components/ui/chart';
import { Link } from "react-router";
import type { DailyStoreAggregatedAverageSales } from '@/lib/api/reports/models';

const labels: string[] = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];

interface DailyAverageSalesProps {
    dailySales: DailyStoreAggregatedAverageSales | null;
}

const chartConfig = {
  dailySales: {
    label: "Daily Sales",
    color: "hsl(210, 100%, 60%)", // Blue
  },
  average: {
    label: "Average",
    color: "hsl(348, 83%, 47%)", // Red
  },
} satisfies ChartConfig;

const DailyAverageSales = ({ dailySales }: DailyAverageSalesProps) => {
    if (!dailySales) {
        return <div className="p-4">No data available.</div>;
    }

    const salesData: number[] = [
        dailySales.monday || 0,
        dailySales.tuesday || 0,
        dailySales.wednesday || 0,
        dailySales.thursday || 0,
        dailySales.friday || 0,
        dailySales.saturday || 0,
        dailySales.sunday || 0,
    ];

    const totalSales = salesData.reduce((sum, val) => sum + val, 0);
    const daysWithData = salesData.filter(val => val > 0).length;
    const averageSales = daysWithData > 0 ? totalSales / daysWithData : 0;

    const today = new Date().getDay();
    const adjustedDayIndex = today === 0 ? 6 : today - 1;
    const todaySales = salesData[adjustedDayIndex] || 0;
    const percentChange = averageSales ? ((todaySales - averageSales) / averageSales) * 100 : 0;

    // Transform data for Recharts
    const chartData = labels.map((day, index) => ({
        day: day.slice(0, 3), // Abbreviate day names (Mon, Tue, etc.)
        fullDay: day,
        dailySales: salesData[index],
        average: averageSales,
        index: index,
    }));

    return (
        <div className="rounded-lg border bg-card p-4 shadow-sm">
            <h3 className="text-lg font-semibold">Daily Average Sales</h3>
            <div className="text-2xl font-bold text-gray-900 dark:text-white">
                ${averageSales.toFixed(2)}
            </div>
            <div className={`text-sm ${percentChange >= 0 ? 'text-green-500' : 'text-red-500'}`}>
                {percentChange >= 0 ? '+' : ''}{percentChange.toFixed(2)}% {percentChange >= 0 ? 'above' : 'below'} Average
            </div>
            <div className="h-16 mt-2">
                <ChartContainer config={chartConfig} className="h-full w-full">
                    <LineChart
                        data={chartData}
                        margin={{
                            top: 5,
                            right: 5,
                            left: 5,
                            bottom: 5,
                        }}
                    >
                        <XAxis
                            dataKey="day"
                            tickLine={false}
                            axisLine={false}
                            className="text-xs"
                            hide
                        />
                        <YAxis
                            tickLine={false}
                            axisLine={false}
                            className="text-xs"
                            hide
                        />
                        <Line
                            type="monotone"
                            dataKey="dailySales"
                            stroke="var(--color-dailySales)"
                            strokeWidth={2}
                            dot={false}
                        />
                        <ReferenceLine
                            y={averageSales}
                            stroke="var(--color-average)"
                            strokeDasharray="5 5"
                            strokeWidth={1}
                        />
                        <ChartTooltip
                            content={({ active, payload }) => {
                                if (active && payload && payload.length) {
                                    const data = payload[0].payload;
                                    return (
                                        <div className="rounded-lg border bg-background p-2 shadow-md">
                                            <div className="grid gap-1">
                                                <div className="font-medium text-xs">{data.fullDay}</div>
                                                <div className="flex items-center justify-between gap-2">
                                                    <span className="text-xs text-muted-foreground">Sales:</span>
                                                    <span className="text-xs font-medium">${data.dailySales.toFixed(2)}</span>
                                                </div>
                                                <div className="flex items-center justify-between gap-2">
                                                    <span className="text-xs text-muted-foreground">Average:</span>
                                                    <span className="text-xs font-medium">${data.average.toFixed(2)}</span>
                                                </div>
                                            </div>
                                        </div>
                                    );
                                }
                                return null;
                            }}
                        />
                    </LineChart>
                </ChartContainer>
            </div>
            <Link to="/reports/sales" className="text-blue-500 hover:underline mt-2 block text-sm">
                View Detailed Report
            </Link>
        </div>
    );
};

export default DailyAverageSales;