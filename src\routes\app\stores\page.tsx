"use client";

import { useEffect } from "react";
import { useDispatch } from "react-redux";
import { getStores } from "@/lib/api/retailStores/service";
import {
  setStores,
  setLoading,
  setError,
} from "@/lib/features/reatailStores/retailStoreSlice";
import { Skeleton } from "@/components/ui/skeleton";
import Cookies from "js-cookie";
import type { Store } from "@/lib/api/retailStores/models";
import { useNavigate } from "react-router";

export default function Page() {
  const dispatch = useDispatch();
  const router = useNavigate();

  useEffect(() => {
    async function fetchStores() {
      dispatch(setLoading(true));
      try {
        const token = Cookies.get("auth_token");
        if (!token) {
          throw new Error("No auth token found");
        }

        const stores: Store[] = await getStores(token) as unknown as Store[];

        dispatch(setStores(stores));
        Cookies.set("active_store", stores[0].id, {
          expires: 1,
          sameSite: "strict",
          path: "/",
        });

        // router.push('/app/items');
        router('/app/dashboard');

      } catch (error) {
        console.error(error);
        dispatch(setError("Failed to load stores"));
      } finally {
        dispatch(setLoading(false));
      }
    }

    fetchStores();
  }, [router, dispatch]);

  return (
    <div className="flex flex-col space-y-4 p-6">
      <div className="flex justify-between items-center">
        <Skeleton className="w-[200px] h-[24px]" />
        <Skeleton className="w-[120px] h-[36px]" />
      </div>
      <div className="space-y-2">
        <Skeleton className="w-full h-[20px]" />
        {[...Array(5)].map((_, index) => (
          <Skeleton key={index} className="w-full h-[40px]" />
        ))}
      </div>
    </div>
  );
}
