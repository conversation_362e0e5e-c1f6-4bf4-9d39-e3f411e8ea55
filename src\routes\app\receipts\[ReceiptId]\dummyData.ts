// dummyData.ts
import type { Receipt, Store, Item } from '@/lib/api/receipts/models';

export const sampleReceipt: Receipt = {
  created_at: new Date().toISOString(),
  id: "3fa85f64-5717-4562-b3fc-2c963f66afa6",
  items: [
    {
      name: "Organic Bananas",
      amount: 7.98,
      quantity: 2,
      store_id: "store123"
    },
    {
      name: "Whole Grain Bread",
      amount: 4.99,
      quantity: 1,
      store_id: "store123"
    },
    {
      name: "Organic Milk 1L",
      amount: 11.98,
      quantity: 2,
      store_id: "store123"
    }
  ],
  receipt_number: "RCP-2024-0001",
  salesperson: "<PERSON>",
  store: {
    name: "Green Market Expres0s",
    address: "123 Healthy Living Street",
    city: "San Francisco",
    country: "United States",
    postal_code: "94105",
    phone: "(*************",
    currency: "USD"
  },
  total: 24.95
};

// Empty receipt for testing
export const emptyReceipt: Receipt = {
  created_at: new Date().toISOString(),
  id: "4fa85f64-5717-4562-b3fc-2c963f66afa6",
  items: [],
  receipt_number: "RCP-2024-0002",
  salesperson: "<PERSON>",
  store: { ...sampleReceipt.store },
  total: 0
};

// Large receipt for testing
export const largeReceipt: Receipt = {
  ...sampleReceipt,
  id: "5fa85f64-5717-4562-b3fc-2c963f66afa6",
  receipt_number: "RCP-2024-0003",
  items: [
    ...sampleReceipt.items,
    {
      name: "Fresh Eggs (12-pack)",
      amount: 6.99,
      quantity: 1,
      store_id: "store123"
    },
    {
      name: "Organic Apples",
      amount: 8.97,
      quantity: 3,
      store_id: "store123"
    }
  ],
  total: 40.91
};
