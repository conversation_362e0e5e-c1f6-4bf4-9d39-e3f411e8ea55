// export default FormStepIndicator;
"use client";

import React from 'react';
import { CheckCircle } from 'lucide-react';
import { cn } from '@/lib/utils';

interface FormStepIndicatorProps {
  currentStep: number;
  totalSteps: number;
  steps?: string[];
}

const FormStepIndicator: React.FC<FormStepIndicatorProps> = ({ 
  currentStep, 
  totalSteps,
  steps = ['User Details', 'Store Details'] 
}) => {
  return (
    <div className="w-full mb-8">
      <ol className="flex items-center w-full">
        {Array.from({ length: totalSteps }).map((_, index) => {
          const isCompleted = index + 1 < currentStep;
          const isActive = index + 1 === currentStep;
          const isLast = index === totalSteps - 1;
          
          return (
            <li 
              key={index} 
              className={cn("flex items-center", isLast ? 'w-auto' : 'w-full')}
            >
              <div className="flex flex-col items-center">
                <div className={cn(
                  "flex items-center justify-center w-8 h-8 rounded-full transition-colors duration-300",
                  isCompleted ? 'bg-primary' : isActive ? 'bg-primary' : 'bg-muted'
                )}>
                  {isCompleted ? (
                    <CheckCircle className="w-5 h-5 text-primary-foreground" />
                  ) : (
                    <span className={cn(
                      "text-sm font-medium",
                      isActive ? 'text-primary-foreground' : 'text-muted-foreground'
                    )}>
                      {index + 1}
                    </span>
                  )}
                </div>
                <span className={cn(
                  "mt-2 text-xs font-medium",
                  isActive ? 'text-primary font-semibold' : isCompleted ? 'text-primary' : 'text-muted-foreground'
                )}>
                  {steps[index] || `Step ${index + 1}`}
                </span>
              </div>
              
              {!isLast && (
                <div className={cn(
                  "w-full h-0.5 mx-2 transition-colors duration-300",
                  index + 1 < currentStep ? 'bg-primary' : 'bg-muted'
                )} />
              )}
            </li>
          );
        })}
      </ol>
    </div>
  );
};

export default FormStepIndicator;