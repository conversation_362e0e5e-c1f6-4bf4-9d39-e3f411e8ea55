import { But<PERSON> } from "@/components/ui/button";
import { MoreHorizontal, Mail } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import type { User, UserInfoResponse } from "@/lib/api/users/models";
import { useNavigate } from "react-router";
import { Badge } from "@/components/ui/badge";
import React from 'react';

interface Column {
  id: string;
  header: string;
  cell: (props: { row: { original: UserInfoResponse } }) => React.ReactNode;
}

export const getColumns = (router: ReturnType<typeof useNavigate>): Column[] => [
  {
    id: "name",
    header: "User Details",
    cell: ({ row }) => {
      const user = row.original;
      return (
        <div className="flex flex-col space-y-1">
          <div 
            className="font-semibold text-base cursor-pointer hover:text-primary"
            onClick={() => router(`/app/users/${user.id}`)}
          >
            {user.name}
          </div>
          <div className="flex items-center space-x-2 text-sm text-muted-foreground">
            <Mail className="h-4 w-4" />
            <span>{user.email}</span>
          </div>
        </div>
      );
    },
  },
  {
    id: "user_defined_role",
    header: "Role",
    cell: ({ row }) => {
      const role = row.original.user_defined_role;
      let color: "default" | "destructive" | "outline" | "secondary" | null | undefined = "secondary";
      switch ((role || '').toLowerCase()) {
        case "admin":
          color = "default";
          break;
        case "manager":
          color = "outline";
          break;
        case "staff":
          color = "secondary";
          break;
        case "cashier":
          color = "default";
          break;
        case "auditor":
          color = "destructive";
          break;
        default:
          color = "secondary";
      }
      return (
        <Badge variant={color}>
          {role ? role.charAt(0).toUpperCase() + role.slice(1) : 'N/A'}
        </Badge>
      );
    },
  },
  {
    id: "hasAccess",
    header: "Status",
    cell: ({ row }) => {
      const status = row.original.hasAccess;
      return (
        <Badge variant={status ? "default" : "destructive"}>
          {status ? "Active" : "Inactive"}
        </Badge>
      );
    },
  },
  {
    id: "plan",
    header: "Plan",
    cell: ({ row }) => {
      const plan = row.original.plan;
      let color: "default" | "destructive" | "outline" | "secondary" | null | undefined = "secondary";
      switch ((plan || '').toLowerCase()) {
        case "free":
          color = "secondary";
          break;
        case "basic":
          color = "default";
          break;
        case "pro":
          color = "outline";
          break;
        case "enterprise":
          color = "destructive";
          break;
        default:
          color = "secondary";
      }
      return (
        <Badge variant={color}>
          {plan ? plan.charAt(0).toUpperCase() + plan.slice(1) : 'N/A'}
        </Badge>
      );
    },
  },
  {
    id: "actions",
    header: "",
    cell: ({ row }) => {
      const user = row.original;
      return (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 p-0">
              <span className="sr-only">Open menu</span>
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>Actions</DropdownMenuLabel>
            <DropdownMenuItem
              onClick={() => {
                router(`/app/users/${user.id}`);
              }}
            >
              View Details
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={() => {
                router(`/app/users/${user.id}/edit`);
              }}
            >
              Edit User
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      );
    },
  },
];
