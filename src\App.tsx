import { BrowserRouter, Routes, Route, Navigate } from 'react-router-dom';
// Root Layout
import RootLayout from './routes/layout';

// Import your components
import HelpPage from './routes/help/page';
import PrivacyPage from './routes/privacy/page';
import TermsPage from './routes/terms/page';
import NotFoundPage from './routes/not-found';
import HomePage from './routes/page';

// App Layout (authenticated routes)
import AppLayout from './routes/app/layout';

// App routes
import CounterPage from './routes/app/counter/page';
import DashboardPage from './routes/app/dashboard/page';
import StoresPage from './routes/app/stores/page';

// Customers
import CustomersPage from './routes/app/customers/page';
import CustomersCreatePage from './routes/app/customers/create/page';
import CustomerDetailPage from './routes/app/customers/[CustomerId]/page';
import CustomerEditPage from './routes/app/customers/[CustomerId]/edit/page';

// Invoices
import InvoicesPage from './routes/app/invoices/page';
import InvoicesCreatePage from './routes/app/invoices/create/page';
import InvoiceDetailPage from './routes/app/invoices/[InvoiceId]/page';
import InvoiceEditPage from './routes/app/invoices/[InvoiceId]/edit/page';

// Items
import ItemsPage from './routes/app/items/page';
import ItemsCreatePage from './routes/app/items/create/page';
import ItemDetailPage from './routes/app/items/[ItemId]/page';
import ItemEditPage from './routes/app/items/[ItemId]/edit/page';

// Items - Categories
import CategoriesPage from './routes/app/items/categories/page';
import CategoriesCreatePage from './routes/app/items/categories/create/page';

// Items - Brands
import BrandsPage from './routes/app/items/brands/page';
import BrandsCreatePage from './routes/app/items/brands/create/page';

// Items - Matrices
import MatricesPage from './routes/app/items/matrices/page';
import MatricesCreatePage from './routes/app/items/matrices/create/page';
import MatrixDetailPage from './routes/app/items/matrices/[MatrixId]/page';
import MatrixEditPage from './routes/app/items/matrices/[MatrixId]/edit/page';

// Items - Exchanges
import ExchangesPage from './routes/app/items/exchanges/page';
import ExchangesCreatePage from './routes/app/items/exchanges/create/page';
import ExchangeDetailPage from './routes/app/items/exchanges/[ExchangeId]/page';
import ExchangeEditPage from './routes/app/items/exchanges/[ExchangeId]/edit/page';

// Items - Returns
import ReturnsPage from './routes/app/items/returns/page';
import ReturnsCreatePage from './routes/app/items/returns/create/page';
import ReturnDetailPage from './routes/app/items/returns/[ReturnId]/page';

// Sales
import SalesPage from './routes/app/sales/page';
import TransactionsPage from './routes/app/sales/transactions/page';

// Refunds
import RefundsPage from './routes/app/refunds/page';
import RefundDetailPage from './routes/app/refunds/[RefundId]/page';

// Receipts
import ReceiptsPage from './routes/app/receipts/page';
import ReceiptDetailPage from './routes/app/receipts/[ReceiptId]/page';

// Suppliers
import SuppliersPage from './routes/app/suppliers/page';
import SuppliersCreatePage from './routes/app/suppliers/create/page';
import SupplierDetailPage from './routes/app/suppliers/[SupplierId]/page';
import SupplierEditPage from './routes/app/suppliers/[SupplierId]/edit/page';

// Users
import UsersPage from './routes/app/users/page';
import UsersCreatePage from './routes/app/users/create/page';
import UserDetailPage from './routes/app/users/[UserId]/page';
import UserEditPage from './routes/app/users/[UserId]/edit/page';
import RolesPage from './routes/app/users/roles/page';

// Reports
import ReportsPage from './routes/app/reports/page';

// Integrations
import MpesaPage from './routes/app/integrations/mpesa/page';

// Auth
import LoginPage from './routes/auth/login/page';
import RegisterPage from './routes/auth/register/page';
import PasswordResetPage from './routes/auth/password-reset/page';
import PasswordResetRequestPage from './routes/auth/password-reset-request/page';

function App() {
  return (
    <BrowserRouter>
      <RootLayout>
        <Routes>
          {/* Public routes */}
          <Route path="/" element={<HomePage />} />
          <Route path="/help" element={<HelpPage />} />
          <Route path="/privacy" element={<PrivacyPage />} />
          <Route path="/terms" element={<TermsPage />} />
          
          {/* Auth routes */}
          <Route path="/auth/login" element={<LoginPage />} />
          <Route path="/auth/register" element={<RegisterPage />} />
          <Route path="/auth/password-reset" element={<PasswordResetPage />} />
          <Route path="/auth/password-reset-request" element={<PasswordResetRequestPage />} />
          
          {/* App routes with AppLayout (authenticated) */}
          <Route path="/app" element={<AppLayout />}>
            <Route path="counter" element={<CounterPage />} />
            <Route path="dashboard" element={<DashboardPage />} />
            <Route path="stores" element={<StoresPage />} />
            
            {/* Customers */}
            <Route path="customers" element={<CustomersPage />} />
            <Route path="customers/create" element={<CustomersCreatePage />} />
            <Route path="customers/:CustomerId" element={<CustomerDetailPage />} />
            <Route path="customers/:CustomerId/edit" element={<CustomerEditPage />} />
            
            {/* Invoices */}
            <Route path="invoices" element={<InvoicesPage />} />
            <Route path="invoices/create" element={<InvoicesCreatePage />} />
            <Route path="invoices/:InvoiceId" element={<InvoiceDetailPage />} />
            <Route path="invoices/:InvoiceId/edit" element={<InvoiceEditPage />} />
            
            {/* Items */}
            <Route path="items" element={<ItemsPage />} />
            <Route path="items/create" element={<ItemsCreatePage />} />
            <Route path="items/:ItemId" element={<ItemDetailPage />} />
            <Route path="items/:ItemId/edit" element={<ItemEditPage />} />
            
            {/* Items - Categories */}
            <Route path="items/categories" element={<CategoriesPage />} />
            <Route path="items/categories/create" element={<CategoriesCreatePage />} />
            
            {/* Items - Brands */}
            <Route path="items/brands" element={<BrandsPage />} />
            <Route path="items/brands/create" element={<BrandsCreatePage />} />
            
            {/* Items - Matrices */}
            <Route path="items/matrices" element={<MatricesPage />} />
            <Route path="items/matrices/create" element={<MatricesCreatePage />} />
            <Route path="items/matrices/:MatrixId" element={<MatrixDetailPage />} />
            <Route path="items/matrices/:MatrixId/edit" element={<MatrixEditPage />} />
            
            {/* Items - Exchanges */}
            <Route path="items/exchanges" element={<ExchangesPage />} />
            <Route path="items/exchanges/create" element={<ExchangesCreatePage />} />
            <Route path="items/exchanges/:ExchangeId" element={<ExchangeDetailPage />} />
            <Route path="items/exchanges/:ExchangeId/edit" element={<ExchangeEditPage />} />
            
            {/* Items - Returns */}
            <Route path="items/returns" element={<ReturnsPage />} />
            <Route path="items/returns/create" element={<ReturnsCreatePage />} />
            <Route path="items/returns/:ReturnId" element={<ReturnDetailPage />} />
            
            {/* Sales */}
            <Route path="sales" element={<SalesPage />} />
            <Route path="sales/transactions" element={<TransactionsPage />} />
            
            {/* Refunds */}
            <Route path="refunds" element={<RefundsPage />} />
            <Route path="refunds/:refundId" element={<RefundDetailPage />} />
            
            {/* Receipts */}
            <Route path="receipts" element={<ReceiptsPage />} />
            <Route path="receipts/:ReceiptId" element={<ReceiptDetailPage />} />
            
            {/* Suppliers */}
            <Route path="suppliers" element={<SuppliersPage />} />
            <Route path="suppliers/create" element={<SuppliersCreatePage />} />
            <Route path="suppliers/:SupplierId" element={<SupplierDetailPage />} />
            <Route path="suppliers/:SupplierId/edit" element={<SupplierEditPage />} />
            
            {/* Users */}
            <Route path="users" element={<UsersPage />} />
            <Route path="users/create" element={<UsersCreatePage />} />
            <Route path="users/:UserId" element={<UserDetailPage />} />
            <Route path="users/:userId/edit" element={<UserEditPage />} />
            <Route path="users/roles" element={<RolesPage />} />
            
            {/* Reports */}
            <Route path="reports" element={<ReportsPage />} />
            
            {/* Integrations */}
            <Route path="integrations/mpesa" element={<MpesaPage />} />
          </Route>
          
          {/* 404 - catch all */}
          <Route path="/404" element={<NotFoundPage />} />
          <Route path="*" element={<Navigate to="/404" replace />} />
        </Routes>
      </RootLayout>
    </BrowserRouter>
  );
}

export default App;