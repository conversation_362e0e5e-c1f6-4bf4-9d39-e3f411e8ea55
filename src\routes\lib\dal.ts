import Cookies from 'js-cookie';

export function verifySession() {
  const sessionCookie = Cookies.get('auth_token');
  
  if (!sessionCookie) {
    return null;
  }

  try {
    // Here you would typically verify the session token with your backend
    // For now, we'll just check if the cookie exists
    return {
      id: sessionCookie,
      // Add any other session data you need
    };
  } catch (error) {
    console.error('Session verification failed:', error);
    return null;
  }
} 