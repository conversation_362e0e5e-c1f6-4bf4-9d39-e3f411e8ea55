"use client";

import React, { useState, useEffect } from 'react';
import SalesSummary from './SalesSummary';
import InventoryOverview from './InventoryOverview';
import FinancialOverview from './FinancialOverview';
import EmployeeSalesPerformance from './EmployeeSalesPerformance';
import DailyAverageSales from './DailyAverageSales';
import Cookies from 'js-cookie';

import { fetchDailyAverageSales } from '@/lib/api/reports/service';
import { fetchEmployeePerformaces } from '@/lib/api/reports/service';
import { fetchFinancialOverview } from '@/lib/api/reports/service';
import { fetchLowStockItems } from '@/lib/api/reports/service';
import { fetchDailyTotalSales, productPerformance } from '@/lib/api/reports/service';

import type { DailyTotalSales, ProductPerformance, EmployeePerformance, MonthlyFinancialData, LowStockItem } from "@/lib/api/reports/models";
import { Link } from "react-router";

function LoadingSpinner() {
    return (
        <div className="min-h-screen flex justify-center items-center bg-gray-50">
            <div className="animate-pulse flex flex-col items-center">
                <div className="h-12 w-12 rounded-full border-4 border-t-blue-500 border-gray-200 animate-spin"></div>
                <p className="mt-4 text-gray-600 font-medium">Loading reports...</p>
            </div>
        </div>
    );
}

export default function DashboardPage() {
    const [salesData, setSalesData] = useState<DailyTotalSales[] | null>(null);
    const [productData, setProductData] = useState<ProductPerformance[] | null>(null);
    const [employeeData, setEmployeeData] = useState<EmployeePerformance[] | null>(null);
    const [financialData, setFinancialData] = useState<MonthlyFinancialData[] | null>(null);
    const [inventoryData, setInventoryData] = useState<LowStockItem[] | null>(null);
    const [dailyAvgSalesData, setDailyAvgSalesData] = useState<any[] | null>(null); // Keep as is

    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    const token = Cookies.get('auth_token');
    const storeId = Cookies.get('active_store');

    useEffect(() => {
        async function fetchData() {
            if (!token || !storeId) {
                setError("Authentication token or store ID is missing.");
                setLoading(false);
                return;
            }

            try {
                const [
                    fetchedSalesData,
                    fetchedProductData,
                    fetchedEmployeeData,
                    fetchedFinancialData,
                    fetchedInventoryData,
                    fetchedDailyAvgSalesData
                ] = await Promise.all([
                    fetchDailyTotalSales(token, storeId),
                    productPerformance(token, storeId),
                    fetchEmployeePerformaces(token, storeId),
                    fetchFinancialOverview(token, storeId),
                    fetchLowStockItems(token, storeId),
                    fetchDailyAverageSales(token, storeId),
                ]);

                setSalesData(fetchedSalesData);
                setProductData(fetchedProductData);
                setEmployeeData(fetchedEmployeeData);
                setFinancialData(fetchedFinancialData);
                setInventoryData(fetchedInventoryData);
                setDailyAvgSalesData(fetchedDailyAvgSalesData);

            } catch (err: any) {
                console.error("Error fetching data:", err);
                setError(err.message || "Failed to load reports. Please try again.");
            } finally {
                setLoading(false);
            }
        }

        fetchData();
    }, [token, storeId]);

    if (loading) {
        return <LoadingSpinner />;
    }

    if (error) {
        return <div className="p-4 text-red-500">{error}</div>;
    }

    return (
        <div className="p-4">
            {/* Breadcrumb navigation */}
            <div className="bg-white shadow-sm">
                <div className="container mx-auto px-4 py-3">
                    <div className="flex text-sm text-gray-500">
                        <Link to="/app/dashboard" className="hover:text-blue-600">Dashboard</Link>
                        <span className="mx-2">/</span>
                        <span className="text-gray-700 font-medium">Reports</span>
                    </div>
                </div>
            </div>
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">

                {/* Overall Sales Performance */}
                <div className="col-span-1 md:col-span-2 lg:col-span-2 rounded-lg border bg-card p-4 shadow-sm">
                    <SalesSummary salesData={salesData} productData={productData} />
                </div>

                {/* Daily average sales */}
                <div className="col-span-1 rounded-lg border bg-card p-4 shadow-sm">
                    <DailyAverageSales dailyAvgSalesData={dailyAvgSalesData} />
                </div>

                {/* Financial Overview */}
                <div className="col-span-1 rounded-lg border bg-card p-4 shadow-sm">
                    <FinancialOverview financialData={financialData} />
                </div>

                {/* Employee Sales Performance */}
                <div className="col-span-1 rounded-lg border bg-card p-4 shadow-sm">
                    <EmployeeSalesPerformance employeeData={employeeData} />
                </div>

                {/* Inventory Management */}
                <div className="col-span-1 md:col-span-2 lg:col-span-1 rounded-lg border bg-card p-4 shadow-sm">
                    <InventoryOverview inventoryData={inventoryData} />
                </div>

            </div>
        </div>
    );
}