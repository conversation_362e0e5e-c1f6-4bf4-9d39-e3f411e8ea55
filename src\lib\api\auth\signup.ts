import type { UserSchema } from "./models";
import type { User } from "../users/models";

import { BASE_URL } from "@/routes/configs/constants";

export async function signup(user: UserSchema): Promise<User> {
    try{
        const response = await fetch(`${BASE_URL}/auth/signup`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(user),
        });
        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || 'Signup failed');
        }
        return response.json();
    } catch (error) {
        throw error;
    }
}