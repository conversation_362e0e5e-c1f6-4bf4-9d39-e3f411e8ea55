import type { ColumnDef } from "@tanstack/react-table";import { But<PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { ArrowUpDown, MoreHorizontal, ShoppingCart, AlertCircle } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import type { Item } from "@/lib/api/items/models";
import type { Category } from "@/lib/api/categories/models";
import type { Brand } from "@/lib/api/brands/models";
import { Badge } from "@/components/ui/badge";
import { useNavigate } from "react-router";

// interface CartItem {
//   id: string;
//   name: string;
//   price: number;
//   quantity: number;
//   image?: string;
// }

export const getColumns = (
  brands: Brand[],
  categories: Category[],
  router: ReturnType<typeof useNavigate>,
  addToCart: (item: Item) => void,
  storeCurrency: string = 'USD'
): ColumnDef<Item>[] => [
    {
      id: "select",
      header: ({ table }) => (
        <Checkbox
          checked={
            table.getIsAllPageRowsSelected() ||
            (table.getIsSomePageRowsSelected() && "indeterminate")
          }
          onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
          aria-label="Select all"
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label="Select row"
        />
      ),
      enableSorting: false,
      enableHiding: false,
    },
    {
      accessorKey: "image",
      header: "Image",
      cell: ({ row }) => {
        const image = row.getValue("image") as string;
        return (
          <div className="relative w-20 h-20 rounded-lg overflow-hidden bg-gray-100 border border-gray-200">
            {image ? (
              <img
                src={image}
                alt={row.getValue("name")}
                className="w-full h-full object-cover"
              />
            ) : (
              <div className="w-full h-full flex items-center justify-center text-gray-400">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-10 w-10" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
              </div>
            )}
          </div>
        );
      },
      enableSorting: false,
    },
    {
      accessorKey: "name",
      header: ({ column }) => (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Product Details
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      ),
      cell: ({ row }) => (
        <div className="flex flex-col space-y-1">
          <div 
            className="font-semibold text-base cursor-pointer hover:text-primary"
            onClick={() => router(`/app/items/${row.original.id}`)}
          >
            {row.getValue("name")}
          </div>
          <div className="flex items-center space-x-2">
            <Badge variant="outline" className="text-xs">
              SKU: {row.original.sku}
            </Badge>
            {row.original.barcode && (
              <Badge variant="outline" className="text-xs">
                Barcode: {row.original.barcode}
              </Badge>
            )}
          </div>
          <div className="text-sm text-gray-500">
            {categories.find(cat => cat.id === row.original.category)?.name} • 
            {brands.find(brand => brand.id === row.original.brand)?.name}
          </div>
        </div>
      ),
      enableSorting: true,
    },
    {
      accessorKey: "quantity",
      header: ({ column }) => (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Stock
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      ),
      cell: ({ row }) => {
        const quantity = row.getValue("quantity") as number;
        const isLowStock = quantity <= 10 && quantity > 0;
        const isOutOfStock = quantity === 0;

        return (
          <div className="flex items-center space-x-2">
            <div className="flex flex-col">
              <span className={`font-medium ${
                isOutOfStock ? 'text-red-500' :
                isLowStock ? 'text-yellow-500' : 'text-green-500'
              }`}>
                {quantity}
              </span>
              {isLowStock && (
                <span className="text-xs text-yellow-500 flex items-center">
                  <AlertCircle className="h-3 w-3 mr-1" />
                  Low Stock
                </span>
              )}
              {isOutOfStock && (
                <span className="text-xs text-red-500 flex items-center">
                  <AlertCircle className="h-3 w-3 mr-1" />
                  Out of Stock
                </span>
              )}
            </div>
          </div>
        );
      },
      enableSorting: true,
    },
    {
      accessorKey: "default_price",
      header: ({ column }) => (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Price
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      ),
      cell: ({ row }) => {
        const price = row.getValue("default_price") as number;
        const hasDiscount = row.original.has_discount;
        const discount = row.original.discount;

        return (
          <div className="flex flex-col">
            <div className="font-semibold text-base">
              {price.toLocaleString('en-US', { style: 'currency', currency: storeCurrency })}
            </div>
            {hasDiscount && discount && (
              <div className="flex items-center space-x-1">
                <Badge variant="secondary" className="text-xs bg-green-100 text-green-700">
                  {discount}% OFF
                </Badge>
                <span className="text-xs text-gray-500 line-through">
                  {(price / (1 - discount / 100)).toLocaleString('en-US', { style: 'currency', currency: storeCurrency })}
                </span>
              </div>
            )}
          </div>
        );
      },
      enableSorting: true,
    },
    {
      id: "actions",
      enableHiding: false,
      cell: ({ row }) => {
        const item = row.original;
        return (
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              className="h-8"
              onClick={() => addToCart(item)}
              disabled={item.quantity === 0}
            >
              <ShoppingCart className="h-4 w-4 mr-2" />
              Add to Cart
            </Button>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="h-8 w-8 p-0">
                  <span className="sr-only">Open menu</span>
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuLabel>Actions</DropdownMenuLabel>
                <DropdownMenuItem
                  onClick={() => {
                    router(`/app/items/${item.id}`);
                  }}
                >
                  View Details
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={() => {
                    router(`/app/items/${item.id}/edit`);
                  }}
                >
                  Edit Item
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        );
      },
    },
  ];
