"use client";

import { MpesaIntegrationForm } from "../MpesaIntegrationForm";
import type { MpesaIntegrationFormValues } from "@/lib/api/integrations/mpesa/models";
import { saveMpesaIntegration } from "@/lib/api/integrations/mpesa/service";
import Cookies from "js-cookie";
import { useNavigate } from "react-router";

const defaultValues: MpesaIntegrationFormValues = {
  business_short_code: "",
  consumer_key: "",
  consumer_secret: "",
  pass_key: "",
  store_id: Cookies.get("active_store") || "",
};

export default function CreateMpesaIntegrationPage() {
  const router = useNavigate();
  const handleSubmit = async (values: MpesaIntegrationFormValues) => {
    await saveMpesaIntegration(values);
    router("/app/integrations/mpesa");
  };

  return (
    <MpesaIntegrationForm
      initialData={defaultValues}
      onSubmit={handleSubmit}
      isEditing={false}
    />
  );
}