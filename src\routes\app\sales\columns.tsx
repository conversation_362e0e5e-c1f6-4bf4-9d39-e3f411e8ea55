"use client";

import type { ColumnDef } from "@tanstack/react-table";import { ArrowUpDown, MoreHorizontal } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import type { Sale, SaleStatus } from "@/lib/api/sales/models";
import type { Item } from "@/lib/api/items/models";
import type { UserInfoResponse } from "@/lib/api/users/models";
import { useNavigate } from "react-router";
import { formatDateTime } from "@/lib/utils/date";
import { formatCurrency } from "@/lib/utils/currency";
import type { Receipt as ReceiptModel } from "@/lib/api/receipts/models";

type Router = ReturnType<typeof useNavigate>;

export const getColumns = (
  users: UserInfoResponse[],
  items: Item[],
  receipts: ReceiptModel[],
  router: Router
): ColumnDef<Sale>[] => [
  {
    accessorKey: "receipt_number",
    header: ({ column }) => {
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Receipt No.
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      const receipt = receipts.find((r) => r.id === row.original.receipt_id);
      return receipt?.receipt_number || "Unknown Receipt";
    },
  },
  {
    accessorKey: "item_id",
    header: ({ column }) => {
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Item
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      const item = items.find((i) => i.id === row.original.item_id);
      return item?.name || "Unknown Item";
    },
  },
  {
    accessorKey: "salesperson_id",
    header: ({ column }) => {
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Salesperson
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      const user = users.find((u) => u.id === row.original.salesperson_id);
      return user?.name || "Unknown User";
    },
  },
  {
    accessorKey: "quantity",
    header: ({ column }) => {
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Quantity
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
  },
  {
    accessorKey: "total",
    header: ({ column }) => {
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Total
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => formatCurrency(row.original.total),
  },
  {
    accessorKey: "status",
    header: ({ column }) => {
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Status
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      // Normalize status to lowercase for comparison
      const statusRaw = row.original.status as string;
      const status = statusRaw.toLowerCase();
      let color = "";
      let label: string = statusRaw.charAt(0).toUpperCase() + statusRaw.slice(1);
      switch (status) {
        case "completed":
          color = "bg-green-100 text-green-800";
          break;
        case "pending":
          color = "bg-yellow-100 text-yellow-800";
          break;
        case "returned":
          color = "bg-blue-100 text-blue-800";
          break;
        case "canceled":
        case "cancelled":
          color = "bg-red-100 text-red-800";
          break;
        default:
          color = "bg-gray-100 text-gray-800";
      }
      return (
        <span className={`px-2 py-1 rounded text-xs font-semibold ${color}`}>{label}</span>
      );
    },
  },
  {
    accessorKey: "sale_time",
    header: ({ column }) => {
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Sale Time
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => formatDateTime(row.original.sale_time),
  },
  {
    id: "actions",
    cell: ({ row }) => {
      const sale = row.original;

      return (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 p-0">
              <span className="sr-only">Open menu</span>
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>Actions</DropdownMenuLabel>
            <DropdownMenuItem
              onClick={() => {
                router(`/app/sales/${sale.id}`);
              }}
            >
              View Details
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={() => {
                router(`/app/sales/${sale.id}/print`);
              }}
            >
              Print Receipt
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      );
    },
  },
];
