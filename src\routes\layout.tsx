import { Helmet } from "react-helmet-async";
import "./globals.css";
import StoreProvider from "./StoreProvider";
import { roboto } from "./ui/fonts";
import { Toaster } from "sonner";
import ChunkErrorHandler from "./components/ChunkErrorHandler";

export default function RootLayout({ children }: { children: React.ReactNode }) {
  return (
    <div className={roboto.className}>
      <Helmet>
        <title>StoreYako</title>
        <meta
          name="description"
          content="Cloud based Inventory Management System serving as Point of Sale (POS)"
        />
      </Helmet>

      <StoreProvider>
        <ChunkErrorHandler />
        {children}
        <Toaster position="top-center" richColors />
      </StoreProvider>
    </div>
  );
}
