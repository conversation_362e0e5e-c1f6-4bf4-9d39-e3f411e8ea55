"use client";

import React from 'react';
import { Button } from '@/components/ui/button';

interface Props {
  children: React.ReactNode;
  onError?: () => void;
}

interface State {
  hasError: boolean;
  error?: Error;
}

export class ScannerErrorBoundary extends React.Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Scanner Error Boundary caught an error:', error, errorInfo);
    
    // Check if it's a chunk loading error
    if (
      error.message?.includes('Loading chunk') ||
      error.name === 'ChunkLoadError' ||
      errorInfo.componentStack?.includes('BarcodeScanner')
    ) {
      console.error('Chunk loading error in scanner component');
      this.props.onError?.();
    }
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="flex flex-col items-center justify-center h-full p-4 text-center">
          <div className="text-red-500 mb-2">Scanner Error</div>
          <div className="text-sm text-muted-foreground mb-4">
            {this.state.error?.message?.includes('Loading chunk') 
              ? 'Failed to load scanner components. This may be due to network issues.'
              : 'An error occurred while loading the scanner.'
            }
          </div>
          <div className="space-y-2">
            <Button 
              onClick={() => {
                this.setState({ hasError: false, error: undefined });
              }}
              className="w-full"
            >
              Try Again
            </Button>
            <Button 
              onClick={() => window.location.reload()} 
              variant="outline"
              className="w-full"
            >
              Refresh Page
            </Button>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}
