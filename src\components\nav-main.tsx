"use client";

import { ChevronRight, type LucideIcon } from "lucide-react";
import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";

import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import {
  SidebarGroup,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
} from "@/components/ui/sidebar";

const SIDEBAR_MENU_STATE_KEY = "sidebar-menu-state";

export function NavMain({
  items,
}: {
  items: {
    title: string;
    url: string;
    icon?: LucideIcon;
    isActive?: boolean;
    items?: {
      title: string;
      url: string;
    }[];
  }[];
}) {
  const router = useNavigate();

  // Initialize state from localStorage or default values
  const [openItems, setOpenItems] = useState<Record<string, boolean>>(() => {
    if (typeof window !== "undefined") {
      try {
        const saved = localStorage.getItem(SIDEBAR_MENU_STATE_KEY);
        if (saved) {
          return JSON.parse(saved);
        }
      } catch (error) {
        console.warn("Failed to parse sidebar menu state from localStorage:", error);
      }
    }

    // Default state: set items with isActive to true
    const defaultState: Record<string, boolean> = {};
    items.forEach((item) => {
      defaultState[item.title] = item.isActive || false;
    });
    return defaultState;
  });

  // Save state to localStorage whenever it changes
  useEffect(() => {
    if (typeof window !== "undefined") {
      try {
        localStorage.setItem(SIDEBAR_MENU_STATE_KEY, JSON.stringify(openItems));
      } catch (error) {
        console.warn("Failed to save sidebar menu state to localStorage:", error);
      }
    }
  }, [openItems]);

  const handleOpenChange = (itemTitle: string, isOpen: boolean) => {
    setOpenItems(prev => ({
      ...prev,
      [itemTitle]: isOpen
    }));
  };

  return (
    <SidebarGroup>
      <SidebarGroupLabel>StoreYako POS System</SidebarGroupLabel>
      <SidebarMenu>
        {items.map((item) => (
          <Collapsible
            key={item.title}
            asChild
            open={openItems[item.title] || false}
            onOpenChange={(isOpen) => handleOpenChange(item.title, isOpen)}
            className="group/collapsible"
          >
            <SidebarMenuItem>
              <CollapsibleTrigger asChild>
                <SidebarMenuButton tooltip={item.title}>
                  {item.icon && <item.icon />}
                  <span>{item.title}</span>
                  <ChevronRight className="ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90" />
                </SidebarMenuButton>
              </CollapsibleTrigger>
              <CollapsibleContent>
                <SidebarMenuSub>
                  {item.items?.map((subItem) => (
                    <SidebarMenuSubItem key={subItem.title}>
                      <SidebarMenuSubButton
                        asChild
                        onClick={() => router(subItem.url)}
                      >
                        <span>{subItem.title}</span>
                      </SidebarMenuSubButton>
                    </SidebarMenuSubItem>
                  ))}
                </SidebarMenuSub>
              </CollapsibleContent>
            </SidebarMenuItem>
          </Collapsible>
        ))}
      </SidebarMenu>
    </SidebarGroup>
  );
}
