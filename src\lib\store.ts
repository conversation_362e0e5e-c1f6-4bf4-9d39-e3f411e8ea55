import { configureStore } from '@reduxjs/toolkit'
import counterReducer from './features/counter/counterSlice'
import retailStoreReducer from './features/reatailStores/retailStoreSlice';


export const makeStore = () => {
  return configureStore({
    reducer: {
        counter: counterReducer,
        retailStores: retailStoreReducer,
        // you can have as many reducers as you want
    }
  })
}

// Infer the type of makeStore
export type AppStore = ReturnType<typeof makeStore>
// Infer the `RootState` and `AppDispatch` types from the store itself
export type RootState = ReturnType<AppStore['getState']>
export type AppDispatch = AppStore['dispatch']