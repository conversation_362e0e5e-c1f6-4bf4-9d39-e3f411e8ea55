// Import the functions you need from the SDKs you need
import { initializeApp } from "firebase/app";
import { getStorage } from "firebase/storage";

// Your web app's Firebase configuration
const firebaseConfig = {
  apiKey: import.meta.env.VITE_FIREBASE_API_KEY || "AIzaSyDgY2qoLHC4LSwlGuCUI3jt9s3C0Ttb7uY",
  authDomain: import.meta.env.VITE_FIREBASE_AUTH_DOMAIN || "storeyako.firebaseapp.com",
  projectId: import.meta.env.VITE_FIREBASE_PROJECT_ID || "storeyako",
  storageBucket: import.meta.env.VITE_FIREBASE_STORAGE_BUCKET || "storeyako.firebasestorage.app",
  messagingSenderId: import.meta.env.VITE_FIREBASE_MESSAGING_SENDER_ID || "886881597264",
  appId: import.meta.env.VITE_FIREBASE_APP_ID || "1:886881597264:web:fbc59a2b375211e78f5e55",
  measurementId: import.meta.env.VITE_FIREBASE_MEASUREMENT_ID || "G-85L892QC4S"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);

// Initialize Firebase Storage and get a reference to the service
export const storage = getStorage(app);
export default app;
