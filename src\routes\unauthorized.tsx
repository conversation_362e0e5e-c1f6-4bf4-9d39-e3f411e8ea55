'use client';

import { <PERSON> } from "react-router";
import { <PERSON><PERSON> } from '@/components/ui/button';
import { ShieldAlert } from 'lucide-react';

export default function Unauthorized() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-slate-50">
      <div className="max-w-md w-full p-8 bg-white rounded-lg shadow-lg text-center">
        <div className="mb-6">
          <ShieldAlert className="w-16 h-16 text-rose-500 mx-auto" />
        </div>
        <h1 className="text-2xl font-bold text-slate-800 mb-4">Unauthorized Access</h1>
        <p className="text-slate-600 mb-8">
          You don&apos;t have permission to access this page. Please log in to continue.
        </p>
        <div className="space-y-4">
          <Link to="/auth/login" className="block">
            <Button className="w-full bg-indigo-600 hover:bg-indigo-700">
              Go to Login
            </Button>
          </Link>
          <Link to="/" className="block">
            <Button variant="outline" className="w-full">
              Return to Home
            </Button>
          </Link>
        </div>
      </div>
    </div>
  );
} 