import React from 'react';
import { useFormContext } from 'react-hook-form';
import { Button } from '@/components/ui/button';
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Mail, Send } from 'lucide-react';

interface PasswordResetRequestFormProps {
  onSubmit: () => void;
  isLoading?: boolean;
}

const PasswordResetRequestForm: React.FC<PasswordResetRequestFormProps> = ({ onSubmit, isLoading }) => {
  const { control, watch } = useFormContext();
  const email = watch('user.email');
  const isFormValid = email?.match(/^[^\s@]+@[^\s@]+\.[^\s@]+$/);

  const onFormSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (isFormValid) onSubmit();
  };

  return (
    <div className="bg-card rounded-lg border p-6 shadow-sm">
      <h2 className="text-2xl font-bold mb-6 text-center">Reset Your Password</h2>
      <p className="text-muted-foreground mb-6 text-center">Enter your email to receive a password reset link</p>
      <form onSubmit={onFormSubmit} className="space-y-4">
        {/* Email Field */}
        <FormField
          control={control}
          name="user.email"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Email Address</FormLabel>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-muted-foreground">
                  <Mail size={18} />
                </div>
                <FormControl>
                  <Input placeholder="<EMAIL>" {...field} className="pl-10" />
                </FormControl>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />
        <Button type="submit" className="w-full mt-6" disabled={!isFormValid || isLoading}>
          Send Reset Link
          <Send className="ml-2 h-4 w-4" />
        </Button>
        <p className="text-xs text-muted-foreground mt-4 text-center">
          By requesting a reset, you agree to our Terms of Service and Privacy Policy
        </p>
      </form>
    </div>
  );
};

export default PasswordResetRequestForm;
