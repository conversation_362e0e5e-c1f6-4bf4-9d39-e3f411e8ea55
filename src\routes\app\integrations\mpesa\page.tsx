// app/app/integrations/mpesa/page.tsx
"use client";

import { useState, useEffect } from "react";
import { Link } from "react-router";;
import {
  <PERSON>,
  CardHeader,
  CardTitle,
  CardDescription,
  CardContent,
  CardFooter,
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { CheckCircle2, AlertCircle, Loader2, Settings, ArrowRight, Smartphone } from "lucide-react";
import { getMpesaIntegration, deleteMpesaIntegration } from "@/lib/api/integrations/mpesa/service";
import type { MpesaIntegration } from "@/lib/api/integrations/mpesa/models";

export default function MpesaIntegrationPage() {
  const [integration, setIntegration] = useState<MpesaIntegration | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);
  const [deleteError, setDeleteError] = useState<string | null>(null);

  useEffect(() => {
    async function loadIntegration() {
      setError(null);
      setIsLoading(true);
      try {
        const data = await getMpesaIntegration();
        setIntegration(data);
      } catch (err) {
        setError(err instanceof Error ? err : new Error("Failed to load M-Pesa integration"));
      } finally {
        setIsLoading(false);
      }
    }
    loadIntegration();
  }, []);

  const handleDelete = async () => {
    try {
      setIsDeleting(true);
      setDeleteError(null);
      if (integration) await deleteMpesaIntegration(integration.id);
      setIntegration(null);
    } catch (err) {
      setDeleteError(err instanceof Error ? err.message : "Failed to delete M-Pesa integration");
    } finally {
      setIsDeleting(false);
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex justify-center items-center bg-background">
        <div className="flex flex-col items-center space-y-4">
          <Loader2 className="h-12 w-12 animate-spin text-primary" />
          <p className="text-muted-foreground font-medium">Loading M-Pesa integration...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex justify-center items-center bg-background p-4">
        <Card className="max-w-md w-full">
          <CardContent className="pt-6">
            <div className="text-destructive flex items-center justify-center mb-4">
              <AlertCircle className="h-12 w-12" />
            </div>
            <h2 className="text-xl font-bold text-center mb-2 text-foreground">Error Loading Integration</h2>
            <p className="text-muted-foreground text-center">{error.message}</p>
            <div className="mt-6 flex justify-center">
              <Button variant="outline" asChild>
                <Link to="/app/integrations">
                  Return to Integrations
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="bg-background min-h-screen">
      <div className="bg-card border-b">
        <div className="container mx-auto px-3 sm:px-4 py-3">
          <div className="flex text-xs sm:text-sm text-muted-foreground items-center flex-wrap">
            <Link to="/app/dashboard" className="hover:text-primary">Dashboard</Link>
            <span className="mx-1 sm:mx-2">/</span>
            <Link to="/app/integrations" className="hover:text-primary">Integrations</Link>
            <span className="mx-1 sm:mx-2">/</span>
            <span className="text-foreground font-medium">M-Pesa</span>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-3 sm:px-4 pt-4 sm:pt-6">
        <Card className="w-full">
          <CardHeader className="pb-4 sm:pb-6">
            <div className="flex items-start sm:items-center space-x-3">
              <div className="bg-primary/10 p-2 rounded-lg flex-shrink-0">
                <Smartphone className="h-5 w-5 sm:h-6 sm:w-6 text-primary" />
              </div>
              <div className="min-w-0 flex-1">
                <CardTitle className="text-xl sm:text-2xl">M-Pesa Integration</CardTitle>
                <CardDescription className="text-sm sm:text-base mt-1">
                  Manage your M-Pesa payment gateway settings
                </CardDescription>
              </div>
            </div>
          </CardHeader>
          <CardContent className="p-4 sm:p-6">
            {deleteError && (
              <Alert variant="destructive" className="mb-4 sm:mb-6">
                <AlertCircle className="h-4 w-4" />
                <AlertTitle>Deletion Error</AlertTitle>
                <AlertDescription>{deleteError}</AlertDescription>
              </Alert>
            )}

            {integration ? (
              <div className="space-y-4 sm:space-y-6">
                <Alert className="border-green-200 bg-green-50 text-green-800 dark:border-green-800 dark:bg-green-950 dark:text-green-200">
                  <CheckCircle2 className="h-4 w-4 text-green-600 dark:text-green-400" />
                  <AlertTitle className="font-semibold">M-Pesa is configured</AlertTitle>
                  <AlertDescription>
                    Your store is connected to M-Pesa for payments.
                  </AlertDescription>
                </Alert>

                <div className="border rounded-lg p-3 sm:p-4 grid grid-cols-1 sm:grid-cols-2 gap-x-4 sm:gap-x-6 gap-y-3 sm:gap-y-4 bg-muted/30">
                  <div className="space-y-1">
                    <p className="text-xs font-medium text-muted-foreground uppercase tracking-wider">Business Short Code</p>
                    <p className="text-sm text-foreground font-mono">
                      {integration.business_short_code}
                    </p>
                  </div>
                  <div className="space-y-1">
                    <p className="text-xs font-medium text-muted-foreground uppercase tracking-wider">Consumer Key</p>
                    <p className="text-sm text-foreground break-all font-mono">
                      {integration.consumer_key ? '••••••••••••••••••••' : <span className="text-muted-foreground italic font-sans">Not Set</span>}
                    </p>
                  </div>
                  <div className="space-y-1">
                    <p className="text-xs font-medium text-muted-foreground uppercase tracking-wider">Consumer Secret</p>
                    <p className="text-sm text-foreground break-all font-mono">
                      {integration.consumer_secret ? '••••••••••••••••••••' : <span className="text-muted-foreground italic font-sans">Not Set</span>}
                    </p>
                  </div>
                  <div className="space-y-1">
                    <p className="text-xs font-medium text-muted-foreground uppercase tracking-wider">Passkey</p>
                    <p className="text-sm text-foreground break-all font-mono">
                      {integration.pass_key ? '••••••••••••••••••••' : <span className="text-muted-foreground italic font-sans">Not Set</span>}
                    </p>
                  </div>
                  <div className="space-y-1 sm:col-span-2">
                    <p className="text-xs font-medium text-muted-foreground uppercase tracking-wider">Callback URL</p>
                    <p className="text-sm text-foreground break-all font-mono">
                      {integration.callback_url || <span className="text-muted-foreground italic font-sans">Not specified</span>}
                    </p>
                  </div>
                </div>

                <div className="flex flex-col sm:flex-row justify-between items-stretch sm:items-center pt-4 gap-3 sm:gap-4 border-t mt-6">
                  <Button variant="outline" asChild className="w-full sm:w-auto">
                    <Link to="/app/integrations/mpesa/create">
                      <Settings className="h-4 w-4 mr-2" />
                      Edit Configuration
                    </Link>
                  </Button>
                  <Button
                    variant="destructive"
                    onClick={handleDelete}
                    disabled={isDeleting}
                    className="w-full sm:w-auto"
                  >
                    {isDeleting ? (
                      <Loader2 className="h-4 w-4 animate-spin mr-2" />
                    ) : null}
                    {isDeleting ? "Removing..." : "Remove Integration"}
                  </Button>
                </div>
              </div>
            ) : (
              <div className="flex flex-col items-center text-center space-y-4 sm:space-y-6 py-6 sm:py-8">
                <Alert className="w-full max-w-md border-blue-200 bg-blue-50 text-blue-800 dark:border-blue-800 dark:bg-blue-950 dark:text-blue-200">
                  <AlertCircle className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                  <AlertTitle className="font-semibold">Not Configured</AlertTitle>
                  <AlertDescription>
                    M-Pesa integration is not yet set up for your store.
                  </AlertDescription>
                </Alert>
                <Button asChild size="lg" className="w-full sm:w-auto">
                  <Link to="/app/integrations/mpesa/create">
                    Set Up M-Pesa Integration
                    <ArrowRight className="h-4 w-4 ml-2" />
                  </Link>
                </Button>
              </div>
            )}
          </CardContent>
          {integration && (
            <CardFooter className="bg-muted/30 border-t py-3">
              <p className="text-xs text-muted-foreground">
                Last updated: {new Date(integration.updated_at || integration.created_at || "").toLocaleString()}
              </p>
            </CardFooter>
          )}
        </Card>
      </div>
    </div>
  );
}