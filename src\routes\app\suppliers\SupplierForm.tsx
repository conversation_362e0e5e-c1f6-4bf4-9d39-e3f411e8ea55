"use client";

import { useState } from 'react';
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { <PERSON>, CardContent, <PERSON>Footer, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { type SupplierSchema } from '@/lib/api/suppliers/models';
import { AlertError } from '@/components/errors';
import { AlertSuccess } from '@/components/success';

const supplierFormSchema = z.object({
    name: z.string().min(1, "Company name is required").max(100, "Company name must be 100 characters or less"),
    phone_number: z.string().max(50, "Phone number must be 50 characters or less").optional(),
    address: z.string().optional(),
    city: z.string().max(128, "City must be 128 characters or less").optional(),
    state: z.string().max(128, "State must be 128 characters or less").optional(),
    country: z.string().max(128, "Country must be 128 characters or less").optional(),
    postal_code: z.string().max(50, "Postal code must be 50 characters or less").optional(),
    contact_person: z.string().max(128, "Contact person must be 128 characters or less"),
    notes: z.string().max(1024, "Notes must be 1024 characters or less").optional(),
});

type SupplierFormData = z.infer<typeof supplierFormSchema>;

interface SupplierFormProps {
  initialData?: SupplierSchema;
  onSubmit: (data: SupplierSchema) => Promise<void>;
  isEditing?: boolean;
  onCancel?: () => void;
}

export function SupplierForm({
  initialData,
  onSubmit,
  isEditing = false,
  onCancel
}: SupplierFormProps) {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<SupplierFormData>({
    resolver: zodResolver(supplierFormSchema),
    defaultValues: initialData || {
      name: '',
      phone_number: '',
      address: '',
      city: '',
      state: '',
      country: '',
      postal_code: '',
      contact_person: '',
      notes: '',
    }
  });

  const handleFormSubmit = async (data: SupplierFormData) => {
    setLoading(true);
    setError(null);
    setSuccess(false);

    try {
      // Convert optional fields to empty strings if undefined
      const submitData: SupplierSchema = {
        ...data,
        phone_number: data.phone_number || '',
        address: data.address || '',
        city: data.city || '',
        state: data.state || '',
        country: data.country || '',
        postal_code: data.postal_code || '',
        notes: data.notes || '',
      };
      
      await onSubmit(submitData);
      setSuccess(true);

      if (!isEditing) {
        reset();
      }
    } catch (error) {
      setError((error as Error).message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card className="w-full max-w-2xl">
      {error && <AlertError message={error} />}
      {success && (
        <AlertSuccess message={`Supplier ${isEditing ? 'updated' : 'created'} successfully`} />
      )}
      <CardHeader>
        <CardTitle>{isEditing ? 'Edit Supplier' : 'Add New Supplier'}</CardTitle>
        <CardDescription>
          {isEditing ? 'Update supplier information' : 'Enter supplier details to create a new record'}
        </CardDescription>
      </CardHeader>
      <form onSubmit={handleSubmit(handleFormSubmit)}>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="name">Company Name *</Label>
              <Input
                id="name"
                className={errors.name ? "border-red-500" : ""}
                {...register("name")}
                placeholder="Enter supplier name"
              />
              {errors.name && (
                <p className="text-sm text-red-500">{errors.name.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="phone_number">Phone Number</Label>
              <Input
                id="phone_number"
                className={errors.phone_number ? "border-red-500" : ""}
                {...register("phone_number")}
                placeholder="Enter phone number"
              />
              {errors.phone_number && (
                <p className="text-sm text-red-500">{errors.phone_number.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="contact_person">Contact Person *</Label>
              <Input
                id="contact_person"
                className={errors.contact_person ? "border-red-500" : ""}
                {...register("contact_person")}
                placeholder="Enter contact person name"
              />
              {errors.contact_person && (
                <p className="text-sm text-red-500">{errors.contact_person.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="postal_code">Postal Code</Label>
              <Input
                id="postal_code"
                className={errors.postal_code ? "border-red-500" : ""}
                {...register("postal_code")}
                placeholder="Enter postal code"
              />
              {errors.postal_code && (
                <p className="text-sm text-red-500">{errors.postal_code.message}</p>
              )}
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="address">Address</Label>
            <Input
              id="address"
              className={errors.address ? "border-red-500" : ""}
              {...register("address")}
              placeholder="Enter street address"
            />
            {errors.address && (
              <p className="text-sm text-red-500">{errors.address.message}</p>
            )}
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="city">City</Label>
              <Input
                id="city"
                className={errors.city ? "border-red-500" : ""}
                {...register("city")}
                placeholder="Enter city"
              />
              {errors.city && (
                <p className="text-sm text-red-500">{errors.city.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="state">State</Label>
              <Input
                id="state"
                className={errors.state ? "border-red-500" : ""}
                {...register("state")}
                placeholder="Enter state"
              />
              {errors.state && (
                <p className="text-sm text-red-500">{errors.state.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="country">Country</Label>
              <Input
                id="country"
                className={errors.country ? "border-red-500" : ""}
                {...register("country")}
                placeholder="Enter country"
              />
              {errors.country && (
                <p className="text-sm text-red-500">{errors.country.message}</p>
              )}
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="notes">Notes</Label>
            <Textarea
              id="notes"
              className={`h-24 ${errors.notes ? "border-red-500" : ""}`}
              {...register("notes")}
              placeholder="Enter any additional notes"
            />
            {errors.notes && (
              <p className="text-sm text-red-500">{errors.notes.message}</p>
            )}
          </div>
        </CardContent>

        <CardFooter className="flex justify-end space-x-2">
          <Button
            variant="outline"
            onClick={onCancel}
            type="button"
          >
            Cancel
          </Button>
          <Button type="submit" disabled={loading}>
            {loading ? (isEditing ? 'Updating...' : 'Creating...') : (isEditing ? 'Update Supplier' : 'Create Supplier')}
          </Button>
        </CardFooter>
      </form>
    </Card>
  );
}
