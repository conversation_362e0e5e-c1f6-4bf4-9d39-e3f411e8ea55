// lib/api/users/onboarding/models.ts

// export interface StoreOnboardingData {
//     address: string;
//     category: string;
//     country: string;
//     currency: string;
//     city: string;
//     email: string;
//     name: string;
//     opening_hours:string;
//     phone_number: string;
//     status: string; // Keep status, but manage it server-side
//   }

  export interface UserOnboardingData {
    email: string;
    name: string;
    password: string;  // Keep for now.  Separate authentication flow recommended.
    plan_id?: number;
    profile?: string;
    role_id?: number;
    store_id?: string;
    user_defined_role_id?: number;
  }

  export interface OnboardingData {
    store: StoreOnboardingData;
    user: UserOnboardingData;
    captcha_token: string;
  }

  export interface OnboardingResponse {
    success: boolean;
    message?: string;
    storeId?: string;
    userId?: string;
  }


export interface StoreOnboardingData {
  name: string;
  email: string;
  category: string;
  phone_number: string;
  country: string;
  address: string;
  city: string;
  postal_code?: string;
  state: string;
  street_address: string;
  tax_rate: number;
  currency: string;
  opening_hours?: string;
  payment_methods: string;
  notes: string;
  status: string;
} 