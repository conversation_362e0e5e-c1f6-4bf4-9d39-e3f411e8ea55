'use client';

import { Link } from "react-router";
import { motion } from 'framer-motion';
import {
    Accordion,
    AccordionContent,
    AccordionItem,
    AccordionTrigger,
} from "@/components/ui/accordion";
import {
    Card,
    CardContent,
    CardDescription,
    CardHeader,
    CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Mail, Phone, MessageSquare, FileText, ArrowLeft } from 'lucide-react';

export default function HelpPage() {
    const faqs = [
        {
            question: "How do I create an account?",
            answer: "To create an account, click on the 'Sign Up' button in the top right corner. Fill in your details and follow the verification process."
        },
        {
            question: "How can I reset my password?",
            answer: "Click on 'Forgot Password' on the login page. Enter your email address, and we'll send you instructions to reset your password."
        },
        {
            question: "What payment methods do you accept?",
            answer: "We accept major credit cards, M-Pesa, and bank transfers. All payments are processed securely."
        },
        // Add more FAQs as needed
    ];

    const handleChatSupport = () => {
        // Implement chat functionality
        // For example, open a chat widget or redirect to chat page
        console.log("Opening chat support...");
    };

    const handleEmailSupport = () => {
        // Open email client with support email
        window.location.href = "mailto:<EMAIL>";
    };

    const handlePhoneSupport = () => {
        // Open phone dialer
        window.location.href = "tel:+************";
    };

    const handleViewDocs = () => {
        // Redirect to documentation page
        // Replace with your actual documentation URL
        window.location.href = "/documentation";
    };

    const supportCategories = [
        {
            icon: <MessageSquare className="h-6 w-6" />,
            title: "Live Chat Support",
            description: "Chat with our support team in real-time",
            action: "Start Chat",
            onClick: handleChatSupport
        },
        {
            icon: <Mail className="h-6 w-6" />,
            title: "Email Support",
            description: "Send us an email, we'll respond within 24 hours",
            action: "Send Email",
            onClick: handleEmailSupport
        },
        {
            icon: <Phone className="h-6 w-6" />,
            title: "Phone Support",
            description: "Call us directly for immediate assistance",
            action: "Call Now",
            onClick: handlePhoneSupport
        },
        {
            icon: <FileText className="h-6 w-6" />,
            title: "Documentation",
            description: "Browse our detailed documentation",
            action: "View Docs",
            onClick: handleViewDocs
        }
    ];

    return (
        <div className="min-h-screen bg-background">
            <div className="container mx-auto px-4 py-8">
                {/* Back button */}
                <motion.div
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    className="mb-6"
                >
                    <Button
                        variant="ghost"
                        onClick={() => window.history.back()}
                        className="flex items-center space-x-2"
                    >
                        <ArrowLeft className="h-4 w-4" />
                        <span>Back</span>
                    </Button>
                </motion.div>

                {/* Header */}
                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="text-center mb-12"
                >
                    <h1 className="text-4xl font-bold mb-4">How can we help you?</h1>
                    <p className="text-muted-foreground text-lg">
                        Find answers to common questions or get in touch with our support team
                    </p>
                </motion.div>

                {/* Support Categories */}
                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.2 }}
                    className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-12"
                >
                    {supportCategories.map((category, index) => (
                        <Card key={index} className="hover:shadow-md transition-shadow">
                            <CardHeader>
                                <div className="w-12 h-12 rounded-full bg-muted flex items-center justify-center mb-4">
                                    {category.icon}
                                </div>
                                <CardTitle>{category.title}</CardTitle>
                                <CardDescription>{category.description}</CardDescription>
                            </CardHeader>
                            <CardContent>
                                <Button
                                    className="w-full"
                                    onClick={category.onClick}
                                >
                                    {category.action}
                                </Button>
                            </CardContent>
                        </Card>
                    ))}
                </motion.div>

                {/* FAQs */}
                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.4 }}
                    className="mb-12"
                >
                    <h2 className="text-2xl font-bold mb-6">Frequently Asked Questions</h2>
                    <Accordion type="single" collapsible className="w-full">
                        {faqs.map((faq, index) => (
                            <AccordionItem key={index} value={`item-${index}`}>
                                <AccordionTrigger>{faq.question}</AccordionTrigger>
                                <AccordionContent>{faq.answer}</AccordionContent>
                            </AccordionItem>
                        ))}
                    </Accordion>
                </motion.div>

                {/* Contact Information */}
                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.6 }}
                    className="text-center"
                >
                    <h2 className="text-2xl font-bold mb-4">Still need help?</h2>
                    <p className="text-muted-foreground mb-6">
                        Our support team is available Monday through Friday, 9am to 5pm EAT
                    </p>
                    <div className="flex justify-center space-x-4">
                        <Button variant="outline" className="flex items-center space-x-2">
                            <Mail className="h-4 w-4" />
                            <span><EMAIL></span>
                        </Button>
                        <Button variant="outline" className="flex items-center space-x-2">
                            <Phone className="h-4 w-4" />
                            <span>+254 7113636151</span>
                        </Button>
                    </div>
                </motion.div>
            </div>
        </div>
    );
}
