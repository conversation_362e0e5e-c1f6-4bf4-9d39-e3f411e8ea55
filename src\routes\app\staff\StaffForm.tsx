"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { AlertError } from "@/components/errors";
import { AlertSuccess } from "@/components/success";
import { ImagePlus, User, Mail, Briefcase, Link } from "lucide-react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";

const staffFormSchema = z.object({
    name: z.string().min(1, "Name is required"),
    email: z.string().email("Invalid email format"),
    user_defined_role_id: z.number().optional(),
    profile: z.string().url("Invalid URL format").optional(),
    image: z.any().optional(),
});

type StaffFormData = z.infer<typeof staffFormSchema>;

interface StaffFormProps {
    initialData?: {
        name: string;
        email: string;
        user_defined_role_id?: number;
        profile?: string;
    };
    onSubmit: (data: StaffFormData) => Promise<void>;
    isEditing?: boolean;
    roles: { id: number; name: string }[];
}

export function StaffForm({
    initialData,
    onSubmit,
    isEditing = false,
    roles,
}: StaffFormProps) {
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [success, setSuccess] = useState(false);
    const [previewImage, setPreviewImage] = useState<string | null>(null);

    const {
        register,
        handleSubmit,
        formState: { errors },
        reset,
        watch,
    } = useForm<StaffFormData>({
        resolver: zodResolver(staffFormSchema),
        defaultValues: initialData || {
            name: "",
            email: "",
            user_defined_role_id: undefined,
            profile: "",
        },
    });

    const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const file = e.target.files?.[0];
        if (file) {
            const reader = new FileReader();
            reader.onloadend = () => {
                setPreviewImage(reader.result as string);
            };
            reader.readAsDataURL(file);
        }
    };

    const handleFormSubmit = async (data: StaffFormData) => {
        setLoading(true);
        setError(null);
        setSuccess(false);

        try {
            await onSubmit(data);
            setSuccess(true);

            if (!isEditing) {
                reset();
                setPreviewImage(null);
            }
        } catch (error) {
            setError((error as Error).message);
        } finally {
            setLoading(false);
        }
    };

    return (
        <Card className="w-full max-w-2xl">
            {error && <AlertError message={error} />}
            {success && (
                <AlertSuccess message={`Staff ${isEditing ? 'updated' : 'created'} successfully`} />
            )}
            <CardHeader className="pb-4">
                <CardTitle className="text-xl font-semibold text-slate-800">
                    {isEditing ? 'Edit Staff Member' : 'Add New Staff Member'}
                </CardTitle>
            </CardHeader>
            <form onSubmit={handleSubmit(handleFormSubmit)}>
                <CardContent className="space-y-4">
                    {/* Profile Image Upload */}
                    <div className="flex flex-col items-center space-y-3">
                        <div className="relative">
                            <Avatar className="w-24 h-24 border-4 border-white shadow-lg">
                                <AvatarImage src={previewImage || initialData?.profile || ""} />
                                <AvatarFallback className="bg-indigo-100 text-indigo-600">
                                    <User className="w-12 h-12" />
                                </AvatarFallback>
                            </Avatar>
                            <label 
                                htmlFor="image-upload"
                                className="absolute bottom-0 right-0 bg-indigo-600 text-white p-1.5 rounded-full cursor-pointer hover:bg-indigo-700 transition-colors"
                            >
                                <ImagePlus className="w-4 h-4" />
                            </label>
                            <input
                                id="image-upload"
                                type="file"
                                accept="image/*"
                                className="hidden"
                                onChange={handleImageChange}
                            />
                        </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="space-y-1.5">
                            <Label htmlFor="name" className="flex items-center gap-1.5 text-sm">
                                <User className="w-3.5 h-3.5 text-slate-500" />
                                Name *
                            </Label>
                            <Input
                                id="name"
                                className={errors.name ? "border-red-500 text-sm" : "text-sm"}
                                {...register("name")}
                                placeholder="Enter staff name"
                            />
                            {errors.name && (
                                <p className="text-xs text-red-500">{errors.name.message}</p>
                            )}
                        </div>

                        <div className="space-y-1.5">
                            <Label htmlFor="email" className="flex items-center gap-1.5 text-sm">
                                <Mail className="w-3.5 h-3.5 text-slate-500" />
                                Email *
                            </Label>
                            <Input
                                id="email"
                                type="email"
                                className={errors.email ? "border-red-500 text-sm" : "text-sm"}
                                {...register("email")}
                                placeholder="Enter email address"
                            />
                            {errors.email && (
                                <p className="text-xs text-red-500">{errors.email.message}</p>
                            )}
                        </div>

                        <div className="space-y-1.5">
                            <Label htmlFor="user_defined_role_id" className="flex items-center gap-1.5 text-sm">
                                <Briefcase className="w-3.5 h-3.5 text-slate-500" />
                                Role
                            </Label>
                            <Select
                                onValueChange={(value) => register("user_defined_role_id").onChange({
                                    target: { value: value ? parseInt(value) : undefined }
                                })}
                            >
                                <SelectTrigger 
                                    id="user_defined_role_id"
                                    className={errors.user_defined_role_id ? "border-red-500 text-sm" : "text-sm"}
                                >
                                    <SelectValue placeholder="Select a role" />
                                </SelectTrigger>
                                <SelectContent>
                                    {roles.map((role) => (
                                        <SelectItem key={role.id} value={role.id.toString()}>
                                            {role.name}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                            {errors.user_defined_role_id && (
                                <p className="text-xs text-red-500">{errors.user_defined_role_id.message}</p>
                            )}
                        </div>

                        <div className="space-y-1.5">
                            <Label htmlFor="profile" className="flex items-center gap-1.5 text-sm">
                                <Link className="w-3.5 h-3.5 text-slate-500" />
                                Profile URL
                            </Label>
                            <Input
                                id="profile"
                                className={errors.profile ? "border-red-500 text-sm" : "text-sm"}
                                {...register("profile")}
                                placeholder="Enter profile URL"
                            />
                            {errors.profile && (
                                <p className="text-xs text-red-500">{errors.profile.message}</p>
                            )}
                        </div>
                    </div>
                </CardContent>

                <CardFooter className="flex justify-end space-x-2 pt-4 border-t">
                    <Button
                        type="button"
                        variant="outline"
                        onClick={() => window.history.back()}
                        className="hover:bg-slate-100 text-sm"
                    >
                        Cancel
                    </Button>
                    <Button 
                        type="submit" 
                        disabled={loading}
                        className="bg-indigo-600 hover:bg-indigo-700 text-sm"
                    >
                        {loading ? (isEditing ? 'Updating...' : 'Creating...') : (isEditing ? 'Update Staff' : 'Create Staff')}
                    </Button>
                </CardFooter>
            </form>
        </Card>
    );
} 