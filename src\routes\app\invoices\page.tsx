"use client";
import { useState, useEffect, useMemo } from "react";
import { useMediaQuery } from "@/hooks/use-media-query";
// Helper to format column headers for card view
function formatHeader(id: string): string {
  return id
    .replace(/_/g, " ")
    .split(" ")
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(" ");
}
import {
  type ColumnFiltersState,
  type SortingState,
  type VisibilityState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";

import { ChevronDown, Trash2, Plus, Loader2 } from "lucide-react";

import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { TablePagination } from "@/components/ui/table-pagination";

import { getRetailStoreClients } from "@/lib/api/clients/service";
import { type Customer } from "@/lib/api/clients/models";

import { fetchInvoices } from "@/lib/api/invoices/services";
import { type Invoice } from "@/lib/api/invoices/models";
import Cookies from "js-cookie";

import { getColumns } from "./columns";
import { getStore } from "@/lib/api/retailStores/service";
import { type Store } from "@/lib/api/retailStores/models";
import { useNavigate } from "react-router";

function LoadingSpinner() {
  return (
    <div className="flex justify-center items-center">
      <Loader2 className="h-8 w-8 animate-spin" />
    </div>
  );
}


export default function InvoicesTable() {
  const router = useNavigate();
  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});
  const [rowSelection, setRowSelection] = useState({});
  const [isInitialLoading, setIsInitialLoading] = useState(true);
  const [store, setStore] = useState<Store | null>(null);
  const [pageIndex, setPageIndex] = useState(0);
  const [pageSize, setPageSize] = useState(10);

  const [data, setInvoices] = useState<Invoice[]>([]);
  const [invoices, setCustomers] = useState<Customer[]>([]);

  const token = Cookies.get("auth_token");
  const store_id = Cookies.get("active_store");
  const isMobile = useMediaQuery("(max-width: 768px)");

  useEffect(() => {
    async function getInvoices() {
      if (token && store_id) {
        setIsInitialLoading(true);
        try {
          const fetchedInvoices = await fetchInvoices(token, store_id);
          setInvoices(fetchedInvoices);
          const fetchedCustomers = await getRetailStoreClients(token, store_id);
          setCustomers(fetchedCustomers);
          const fetchedStore = await getStore(store_id, token);
          setStore(fetchedStore);
        } catch (error) {
          console.error("Error fetching invoices:", error);
        } finally {
          setIsInitialLoading(false);
        }
      }
    }
    getInvoices();
  }, [store_id, token]);

  const handleDeleteSelected = async () => {
    if (!token || !store_id) {
      console.error("Authentication token or store ID is missing.");
      return;
    }
    const selectedRows = table.getSelectedRowModel().rows;
    const selectedCustomerIds = selectedRows.map((row) => row.original.id);

    try {
      for (const invoiceId of selectedCustomerIds) {
        // await deleteCustomer(token, store_id, invoiceId);
        router(0);
      }
      setCustomers((prev) =>
        prev.filter((invoice) => !selectedCustomerIds.includes(invoice.id)),
      );
      setRowSelection({});
      setIsDeleteDialogOpen(false);
    } catch (error) {
      console.error("Error deleting invoices:", error);
    }
  };

  const columns = useMemo(() => getColumns({ customers: invoices, router, store: store! }), [invoices, router, store]);

  const table = useReactTable({
    data,
    columns,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
    },
  });

  const filteredRows = table.getFilteredRowModel().rows;
  const sortedRows = table.getSortedRowModel().rows;
  const pageCount = Math.ceil(sortedRows.length / pageSize);
  const start = pageIndex * pageSize;
  const end = start + pageSize;
  const paginatedRows = sortedRows.slice(start, end);

  const showSelectedItems = (
    <ul className="mt-2 space-y-1">
      {table.getSelectedRowModel().rows.map((row) => (
        <li key={row.id} className="text-sm text-muted-foreground">• {row.original.invoice_number}</li>
      ))}
    </ul>
  );

  return (
    <div className="w-full">
      <div className="flex flex-col items-start sm:flex-row sm:items-center gap-4 py-4">
        <Input
          placeholder="Filter invoices..."
          value={(table.getColumn("invoice_number")?.getFilterValue() as string) ?? ""}
          onChange={(event) => table.getColumn("invoice_number")?.setFilterValue(event.target.value)}
          className="w-full sm:max-w-sm"
          disabled={isInitialLoading}
        />
        <Button
          onClick={() => router("/app/invoices/create")}
          className="w-full sm:w-auto mt-2 sm:mt-0"
          disabled={isInitialLoading}
        >
          <Plus className="mr-2 h-4 w-4" />
          New Invoice
        </Button>
        <div className="flex flex-col sm:flex-row items-start sm:items-center gap-2 mt-2 sm:mt-0">
          {Object.keys(rowSelection).length > 0 && (
            <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
              <DialogTrigger asChild>
                <Button variant="destructive" size="sm" disabled={isInitialLoading}>
                  <Trash2 className="mr-2 h-4 w-4" /> Delete Selected
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Delete Invoices</DialogTitle>
                  <DialogDescription>
                    Are you sure you want to delete the selected invoice(s)?
                    {showSelectedItems}
                    <b>This action cannot be undone. </b>
                  </DialogDescription>
                </DialogHeader>
                <div className="flex justify-end space-x-2">
                  <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
                    Cancel
                  </Button>
                  <Button variant="destructive" onClick={handleDeleteSelected}>
                    Delete
                  </Button>
                </div>
              </DialogContent>
            </Dialog>
          )}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" disabled={isInitialLoading} className="w-full sm:w-auto">
                Columns <ChevronDown className="ml-2 h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              {table
                .getAllColumns()
                .filter((column) => column.getCanHide())
                .map((column) => (
                  <DropdownMenuCheckboxItem
                    key={column.id}
                    className="capitalize"
                    checked={column.getIsVisible()}
                    onCheckedChange={(value) => column.toggleVisibility(!!value)}
                  >
                    {column.id}
                  </DropdownMenuCheckboxItem>
                ))}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {/* --- START OF CONDITIONAL RENDERING --- */}

      {/*-- TABLE VIEW FOR DESKTOP --*/}
      {!isMobile && (
        <div className="rounded-md border relative w-full overflow-auto">
          <Table className="w-full caption-bottom text-sm">
            <TableHeader>
              {table.getHeaderGroups().map((headerGroup) => (
                <TableRow key={headerGroup.id}>
                  {headerGroup.headers.map((header) => (
                    <TableHead key={header.id}>
                      {header.isPlaceholder
                        ? null
                        : flexRender(header.column.columnDef.header, header.getContext())}
                    </TableHead>
                  ))}
                </TableRow>
              ))}
            </TableHeader>
            <TableBody>
              {isInitialLoading ? (
                <TableRow>
                  <TableCell colSpan={table.getAllColumns().length} className="h-24 text-center">
                    <LoadingSpinner />
                  </TableCell>
                </TableRow>
              ) : paginatedRows.length ? (
                paginatedRows.map((row) => (
                  <TableRow key={row.id} data-state={row.getIsSelected() ? "selected" : undefined}>
                    {row.getVisibleCells().map((cell) => (
                      <TableCell key={cell.id}>
                        {flexRender(cell.column.columnDef.cell, cell.getContext())}
                      </TableCell>
                    ))}
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={table.getAllColumns().length} className="h-24 text-center">
                    <div className="flex flex-col items-center justify-center text-muted-foreground">
                      <p>No invoices found.</p>
                      <Button
                        variant="link"
                        onClick={() => router("/app/invoices/create")}
                        className="mt-2"
                      >
                        Create your first invoice
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
      )}

      {/*-- CARD VIEW FOR MOBILE (INVOICES) --*/}
      {isMobile && (
        <div className="grid grid-cols-1 gap-4">
          {isInitialLoading && <LoadingSpinner />}
          {!isInitialLoading && paginatedRows.length === 0 && (
            <div className="text-center text-muted-foreground p-4">No invoices found.</div>
          )}
          {paginatedRows.map(row => {
            // Find the special cells for custom placement
            const selectCell = row.getVisibleCells().find(cell => cell.column.id === 'select');
            const actionsCell = row.getVisibleCells().find(cell => cell.column.id === 'actions');
            // Filter out special cells AND the invoice_number to render the rest as key-value pairs
            const dataCells = row.getVisibleCells().filter(
              cell => cell.column.id !== 'select' && cell.column.id !== 'actions' && cell.column.id !== 'invoice_number'
            );
            return (
              <div
                key={row.id}
                className="bg-card text-card-foreground border rounded-lg p-4 space-y-3 relative"
                data-state={row.getIsSelected() ? "selected" : undefined}
              >
                {/* Part 1: Header with Invoice Number, Checkbox, and Actions Menu */}
                <div className="flex justify-between items-start mb-2">
                  <label className="flex items-center space-x-3">
                    {selectCell && flexRender(selectCell.column.columnDef.cell, selectCell.getContext())}
                    <span className="font-bold text-lg break-words">
                      {row.getValue("invoice_number")}
                    </span>
                  </label>
                  <div className="absolute top-2 right-2">
                    {actionsCell && flexRender(actionsCell.column.columnDef.cell, actionsCell.getContext())}
                  </div>
                </div>
                <hr className="border-border" />
                {/* Part 2: Details with proper alignment */}
                <div className="space-y-3 pt-2">
                  {dataCells.map(cell => (
                    <div key={cell.id} className="grid grid-cols-[110px,1fr] items-center text-sm gap-x-4">
                      <div className="font-medium text-muted-foreground">
                        {formatHeader(cell.column.id)}
                      </div>
                      <div className="text-right break-words">
                        {flexRender(cell.column.columnDef.cell, cell.getContext())}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            );
          })}
        </div>
      )}
      {/* --- END OF CONDITIONAL RENDERING --- */}

      {/* Mobile-Responsive Pagination */}
      <div className="flex flex-col sm:flex-row items-center justify-between space-y-4 sm:space-y-0 sm:space-x-2 py-4">
        <div className="text-sm text-muted-foreground text-center sm:text-left">
          {isInitialLoading
            ? "Loading..."
            : `${table.getFilteredSelectedRowModel().rows.length} of ${table.getFilteredRowModel().rows.length} row(s) selected.`}
        </div>

        {/* Desktop Pagination */}
        {!isMobile && (
          <TablePagination
            pageIndex={pageIndex}
            pageSize={pageSize}
            pageCount={pageCount}
            totalItems={filteredRows.length}
            onPageChange={setPageIndex}
            onPageSizeChange={setPageSize}
          />
        )}

        {/* Mobile Pagination */}
        {isMobile && (
          <div className="flex items-center space-x-4">
            <Button
              onClick={() => setPageIndex(Math.max(0, pageIndex - 1))}
              disabled={pageIndex === 0 || isInitialLoading}
            >
              Previous
            </Button>
            <span className="text-sm font-medium">
              Page {pageIndex + 1} of {Math.max(1, pageCount)}
            </span>
            <Button
              onClick={() => setPageIndex(Math.min(pageCount - 1, pageIndex + 1))}
              disabled={pageIndex >= pageCount - 1 || isInitialLoading}
            >
              Next
            </Button>
          </div>
        )}
      </div>
    </div>
  );
}