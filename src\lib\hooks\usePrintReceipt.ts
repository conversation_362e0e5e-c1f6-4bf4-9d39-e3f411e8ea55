import type { Receipt, Item as ReceiptItem } from "@/lib/api/receipts/models"; // Renamed to avoid conflict

export const usePrintReceipt = () => {
  const printReceipt = (receipt: Receipt) => {
    const printWindow = window.open('', '_blank');
    if (!printWindow) {
      console.error("Failed to open print window. Ensure pop-ups are not blocked.");
      return;
    }

    // --- VAT & Subtotal Calculation ---
    let subtotal = 0;
    let totalVat = 0;
    const currency = receipt.store.currency || '';

    receipt.items.forEach((item: ReceiptItem) => {
      if (item.has_vat && item.vat_percentage && item.vat_percentage > 0) {
        // If amount includes VAT, calculate the base amount and the VAT amount
        const preVatAmount = item.amount / (1 + item.vat_percentage / 100);
        const vatOnItem = item.amount - preVatAmount;
        subtotal += preVatAmount;
        totalVat += vatOnItem;
      } else {
        // If no VAT, the full amount is part of the subtotal
        subtotal += item.amount;
      }
    });
    // --- End Calculation ---

    const styles = `
      @page {
        size: 80mm auto;
        margin: 0;
      }
      body {
        font-family: 'Inter', 'Courier New', Courier, monospace;
        margin: 0;
        padding: 15px 10px;
        font-size: 12px;
        line-height: 1.5;
        box-sizing: border-box;
      }
      .receipt {
        width: 80mm;
        margin: 0 auto;
        color: #000;
      }
      .header {
        text-align: left;
        margin-bottom: 15px;
        padding-bottom: 10px;
        border-bottom: 1px dashed #000;
      }
      .header h2 {
        font-size: 18px;
        margin: 0 0 5px 0;
        text-transform: uppercase;
        font-weight: bold;
      }
      .header p { margin: 2px 0; font-size: 11px; line-height: 1.3; }
      .info {
        margin-bottom: 15px;
        padding: 10px 0;
        border-bottom: 1px dashed #000;
      }
      .info p { margin: 4px 0; font-size: 11px; overflow: hidden; }
      .info p strong { float: left; margin-right: 10px; }
      .info p span { float: right; }
      .items { margin-bottom: 15px; width: 100%; }
      .items table { width: 100%; border-collapse: collapse; }
      .items th, .items td {
        padding: 6px 4px;
        font-size: 12px;
        line-height: 1.3;
        vertical-align: top; /* Align content to the top */
      }
      .items th {
        text-align: left;
        border-bottom: 1px solid #000;
        font-size: 11px;
        text-transform: uppercase;
        font-weight: bold;
      }
      .items .item-name small { /* Style for the VAT info */
        display: block;
        font-size: 10px;
        color: #333;
        font-style: italic;
      }
      .items td:nth-child(2) { text-align: center; }
      .items td:nth-child(3) { text-align: right; }
      .total {
        border-top: 1px dashed #000;
        margin-top: 10px;
        padding-top: 8px;
        overflow: hidden; /* Clear floats */
      }
      .total p {
        margin: 4px 0;
        font-size: 12px;
        clear: both;
        overflow: hidden;
      }
      .total p.grand-total {
        font-weight: bold;
        font-size: 14px;
        margin-top: 8px;
        padding-top: 8px;
        border-top: 1px solid #000;
      }
      .total p span { float: left; }
      .total p strong { float: right; }
      .total p.grand-total strong {
        font-size: 16px;
      }
      .footer {
        text-align: left;
        margin-top: 15px;
        padding-top: 10px;
        border-top: 1px dashed #000;
      }
      .footer p { margin: 4px 0; font-size: 11px; line-height: 1.3; }
      .powered-by {
        margin-top: 15px;
        text-align: left;
        font-size: 10px;
        color: #666;
        padding-top: 5px;
        border-top: 1px dashed #eee;
      }
    `;

    const date = new Date(receipt.created_at);
    const formattedDate = date.toLocaleDateString('en-US', { year: 'numeric', month: 'short', day: 'numeric' });
    const formattedTime = date.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit', hour12: true });

    const itemsTable = receipt.items.map((item: ReceiptItem) => {
      const vatInfo = (item.has_vat && item.vat_percentage)
        ? `<small>(VAT ${item.vat_percentage.toFixed(1)}%)</small>`
        : '';
      return `
        <tr>
          <td class="item-name">${item.name}${vatInfo}</td>
          <td>${item.quantity}</td>
          <td>${currency} ${item.amount.toFixed(2)}</td>
        </tr>
      `;
    }).join('');

    // Create the total breakdown section
    const totalSection = `
      <div class="total">
        <p><span>Subtotal:</span> <strong>${currency} ${subtotal.toFixed(2)}</strong></p>
        ${totalVat > 0 ? `<p><span>Total VAT:</span> <strong>${currency} ${totalVat.toFixed(2)}</strong></p>` : ''}
        <p class="grand-total"><span>Total:</span> <strong>${currency} ${receipt.total.toFixed(2)}</strong></p>
      </div>
    `;

    printWindow.document.write(`
      <!DOCTYPE html>
      <html>
        <head>
          <title>Receipt ${receipt.receipt_number}</title>
          <style>${styles}</style>
          <Link to="https://fonts.googleapis.com/css2?family=Inter:wght@400;700&display=swap" rel="stylesheet">
        </head>
        <body>
          <div class="receipt">
            <div class="header">
              <h2>${receipt.store.name}</h2>
              ${receipt.store.address ? `<p>${receipt.store.address}</p>` : ''}
              ${receipt.store.city || ''}${receipt.store.country ? `, ${receipt.store.country}` : ''}</p>
              ${receipt.store.phone ? `<p>Tel: ${receipt.store.phone}</p>` : ''}
              ${receipt.store.postal_code ? `<p>P.O BOX ${receipt.store.postal_code}</p>` : ''}
            </div>
            
            <div class="info">
              <p><strong>Receipt #:</strong> <span>${receipt.receipt_number}</span></p>
              <p><strong>Date:</strong> <span>${formattedDate}</span></p>
              <p><strong>Time:</strong> <span>${formattedTime}</span></p>
            </div>

            <div class="items">
              <table>
                <thead>
                  <tr>
                    <th>Item</th>
                    <th style="text-align: center">Qty</th>
                    <th style="text-align: right">Amount</th>
                  </tr>
                </thead>
                <tbody>
                  ${itemsTable}
                </tbody>
              </table>
            </div>

            ${totalSection}

            <div class="footer">
              <p><strong>Served by:</strong> ${receipt.salesperson}</p>
              <p>Thank you for your business!</p>
              <p>Please come again</p>
            </div>
            
            <div class="powered-by">
              Powered by StoreYako POS
            </div>
          </div>
        </body>
      </html>
    `);

    setTimeout(() => {
      printWindow.print();
      setTimeout(() => {
        printWindow.close();
      }, 1000);
    }, 500);
  };

  return { printReceipt };
};